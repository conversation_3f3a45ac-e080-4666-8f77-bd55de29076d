import mongoose, { Document, Schema } from 'mongoose';

export interface IPhoto {
  url: string;
  alt: string;
  category: 'main' | '1-seater-ac' | '1-seater-non-ac' | '2-seater-ac' | '2-seater-non-ac' | 
           '3-seater-ac' | '3-seater-non-ac' | '4-seater-ac' | '4-seater-non-ac' | 
           'common-area' | 'kitchen' | 'bathroom' | 'exterior';
}

export interface IPricing {
  '1-seater': {
    ac: number;
    nonAc: number;
  };
  '2-seater': {
    ac: number;
    nonAc: number;
  };
  '3-seater': {
    ac: number;
    nonAc: number;
  };
  '4-seater': {
    ac: number;
    nonAc: number;
  };
}

export interface IRoomSizes {
  '1-seater': string;
  '2-seater': string;
  '3-seater': string;
  '4-seater': string;
}

export interface IFoodMenu {
  category: string;
  items: {
    name: string;
    price: number;
    description?: string;
  }[];
}

export interface ICaretaker {
  name: string;
  phone: string;
  availability: string;
}

export interface IHostel extends Document {
  name: string;
  headline: string;
  description: string;
  address: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  photos: IPhoto[];
  pricing: IPricing;
  roomSizes: IRoomSizes;
  
  // Amenities
  amenities: {
    wifi: boolean;
    parking: boolean;
    laundry: boolean;
    meals: boolean;
    ac: boolean;
    powerBackup: boolean;
    studyRoom: boolean;
    commonRoom: boolean;
    gym: boolean;
    indoorGames: boolean;
    outdoorGames: boolean;
    library: boolean;
    medical: boolean;
    security: boolean;
    cctv: boolean;
    biometric: boolean;
    other: string[];
  };
  
  // Location details
  nearbyLandmarks: string[];
  nearestColleges: {
    name: string;
    distance: string;
  }[];
  
  // Food and dining
  foodMenu: IFoodMenu[];
  collegeLunchProvision: boolean;
  
  // Security and rules
  securityFeatures: {
    cctv: boolean;
    securityGuard: boolean;
    biometricAccess: boolean;
    timeRestrictions: {
      enabled: boolean;
      inTime: string;
      outTime: string;
    };
  };
  
  // Staff
  caretaker: ICaretaker;
  
  // Facilities
  gymFacilities: string[];
  indoorSportsFacilities: string[];
  
  // Events and activities
  eventsAndFests: {
    name: string;
    description: string;
    frequency: string;
  }[];
  
  // Travel facilities
  travelFacilities: string[];
  
  // Contact information
  owner: {
    name: string;
    phone: string;
    email?: string;
  };
  
  // Metadata
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const PhotoSchema = new Schema<IPhoto>({
  url: { type: String, required: true },
  alt: { type: String, required: true },
  category: { 
    type: String, 
    required: true,
    enum: ['main', '1-seater-ac', '1-seater-non-ac', '2-seater-ac', '2-seater-non-ac', 
           '3-seater-ac', '3-seater-non-ac', '4-seater-ac', '4-seater-non-ac', 
           'common-area', 'kitchen', 'bathroom', 'exterior']
  }
});

const PricingSchema = new Schema<IPricing>({
  '1-seater': {
    ac: { type: Number, required: true },
    nonAc: { type: Number, required: true }
  },
  '2-seater': {
    ac: { type: Number, required: true },
    nonAc: { type: Number, required: true }
  },
  '3-seater': {
    ac: { type: Number, required: true },
    nonAc: { type: Number, required: true }
  },
  '4-seater': {
    ac: { type: Number, required: true },
    nonAc: { type: Number, required: true }
  }
});

const HostelSchema = new Schema<IHostel>({
  name: { type: String, required: true },
  headline: { type: String, required: true },
  description: { type: String, required: true },
  
  address: {
    street: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    pincode: { type: String, required: true },
    coordinates: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true }
    }
  },
  
  photos: [PhotoSchema],
  pricing: { type: PricingSchema, required: true },
  
  roomSizes: {
    '1-seater': { type: String, required: true },
    '2-seater': { type: String, required: true },
    '3-seater': { type: String, required: true },
    '4-seater': { type: String, required: true }
  },
  
  amenities: {
    wifi: { type: Boolean, default: false },
    parking: { type: Boolean, default: false },
    laundry: { type: Boolean, default: false },
    meals: { type: Boolean, default: false },
    ac: { type: Boolean, default: false },
    powerBackup: { type: Boolean, default: false },
    studyRoom: { type: Boolean, default: false },
    commonRoom: { type: Boolean, default: false },
    gym: { type: Boolean, default: false },
    indoorGames: { type: Boolean, default: false },
    outdoorGames: { type: Boolean, default: false },
    library: { type: Boolean, default: false },
    medical: { type: Boolean, default: false },
    security: { type: Boolean, default: false },
    cctv: { type: Boolean, default: false },
    biometric: { type: Boolean, default: false },
    other: [{ type: String }]
  },
  
  nearbyLandmarks: [{ type: String }],
  nearestColleges: [{
    name: { type: String, required: true },
    distance: { type: String, required: true }
  }],
  
  foodMenu: [{
    category: { type: String, required: true },
    items: [{
      name: { type: String, required: true },
      price: { type: Number, required: true },
      description: { type: String }
    }]
  }],
  
  collegeLunchProvision: { type: Boolean, default: false },
  
  securityFeatures: {
    cctv: { type: Boolean, default: false },
    securityGuard: { type: Boolean, default: false },
    biometricAccess: { type: Boolean, default: false },
    timeRestrictions: {
      enabled: { type: Boolean, default: false },
      inTime: { type: String },
      outTime: { type: String }
    }
  },
  
  caretaker: {
    name: { type: String, required: true },
    phone: { type: String, required: true },
    availability: { type: String, required: true }
  },
  
  gymFacilities: [{ type: String }],
  indoorSportsFacilities: [{ type: String }],
  
  eventsAndFests: [{
    name: { type: String, required: true },
    description: { type: String, required: true },
    frequency: { type: String, required: true }
  }],
  
  travelFacilities: [{ type: String }],
  
  owner: {
    name: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String }
  },
  
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
});

// Create indexes for better query performance
HostelSchema.index({ 'address.coordinates': '2dsphere' });
HostelSchema.index({ name: 'text', headline: 'text', description: 'text' });
HostelSchema.index({ isActive: 1 });

export default mongoose.models.Hostel || mongoose.model<IHostel>('Hostel', HostelSchema);
