import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Hostel from '@/models/Hostel';
import { filterHostels, sortHostelsByDistance } from '@/lib/utils';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const search = searchParams.get('search') || '';
    const seaterType = searchParams.get('seaterType') || '';
    const acType = searchParams.get('acType') || '';
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const amenities = searchParams.get('amenities')?.split(',').filter(Boolean) || [];
    const sortBy = searchParams.get('sortBy') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    const userLat = searchParams.get('userLat');
    const userLng = searchParams.get('userLng');
    const maxDistance = searchParams.get('maxDistance');
    
    // Build query
    let query: any = { isActive: true };
    
    // Search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { headline: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { 'address.city': { $regex: search, $options: 'i' } },
        { 'address.state': { $regex: search, $options: 'i' } }
      ];
    }
    
    // Get all hostels first
    let hostels = await Hostel.find(query).lean();
    
    // Apply filters
    const filters: any = {};
    
    if (seaterType && seaterType !== 'all') {
      filters.seaterType = seaterType;
    }
    
    if (acType && acType !== 'all') {
      filters.acType = acType;
    }
    
    if (minPrice && maxPrice) {
      filters.priceRange = [parseInt(minPrice), parseInt(maxPrice)];
    }
    
    if (amenities.length > 0) {
      filters.amenities = amenities;
    }
    
    if (maxDistance && userLat && userLng) {
      filters.maxDistance = parseFloat(maxDistance);
      filters.userLocation = {
        lat: parseFloat(userLat),
        lng: parseFloat(userLng)
      };
    }
    
    // Apply filters
    hostels = filterHostels(hostels, filters);
    
    // Sort hostels
    if (sortBy === 'distance' && userLat && userLng) {
      hostels = sortHostelsByDistance(
        hostels,
        parseFloat(userLat),
        parseFloat(userLng)
      );
    } else {
      hostels.sort((a, b) => {
        let aValue, bValue;
        
        switch (sortBy) {
          case 'price':
            // Get minimum price for sorting
            const aPrices = Object.values(a.pricing).flatMap((p: any) => [p.ac, p.nonAc]);
            const bPrices = Object.values(b.pricing).flatMap((p: any) => [p.ac, p.nonAc]);
            aValue = Math.min(...aPrices);
            bValue = Math.min(...bPrices);
            break;
          case 'name':
            aValue = a.name.toLowerCase();
            bValue = b.name.toLowerCase();
            break;
          default:
            aValue = a[sortBy];
            bValue = b[sortBy];
        }
        
        if (sortOrder === 'desc') {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        } else {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        }
      });
    }
    
    // Pagination
    const skip = (page - 1) * limit;
    const paginatedHostels = hostels.slice(skip, skip + limit);
    const totalHostels = hostels.length;
    const totalPages = Math.ceil(totalHostels / limit);
    
    return NextResponse.json({
      hostels: paginatedHostels,
      pagination: {
        currentPage: page,
        totalPages,
        totalHostels,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
    
  } catch (error) {
    console.error('Get hostels error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    await connectDB();
    
    const hostelData = await request.json();
    
    // Create new hostel
    const hostel = new Hostel(hostelData);
    await hostel.save();
    
    return NextResponse.json({
      message: 'Hostel created successfully',
      hostel
    }, { status: 201 });
    
  } catch (error) {
    console.error('Create hostel error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
