{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached: MongooseCache = global.mongoose || { conn: null, promise: null };\n\nif (!global.mongoose) {\n  global.mongoose = cached;\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAWA,IAAI,SAAwB,OAAO,QAAQ,IAAI;IAAE,MAAM;IAAM,SAAS;AAAK;AAE3E,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;AACpB;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa;IACjD;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nexport interface I<PERSON>ser extends Document {\n  email: string;\n  password: string;\n  role: 'admin' | 'user';\n  name: string;\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\nconst UserSchema = new Schema<IUser>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6\n  },\n  role: {\n    type: String,\n    enum: ['admin', 'user'],\n    default: 'user'\n  },\n  name: {\n    type: String,\n    required: true,\n    trim: true\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error: any) {\n    next(error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Create indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAaA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAS;SAAO;QACvB,SAAS;IACX;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAY;QACnB,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAyB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,iBAAiB;AACjB,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport User from '@/models/User';\nimport jwt from 'jsonwebtoken';\n\nexport async function POST(request: NextRequest) {\n  try {\n    await connectDB();\n    \n    const { email, password } = await request.json();\n    \n    if (!email || !password) {\n      return NextResponse.json(\n        { error: 'Email and password are required' },\n        { status: 400 }\n      );\n    }\n    \n    // Check if user exists\n    const user = await User.findOne({ email: email.toLowerCase() });\n    if (!user) {\n      return NextResponse.json(\n        { error: 'Invalid credentials' },\n        { status: 401 }\n      );\n    }\n    \n    // Check password\n    const isPasswordValid = await user.comparePassword(password);\n    if (!isPasswordValid) {\n      return NextResponse.json(\n        { error: 'Invalid credentials' },\n        { status: 401 }\n      );\n    }\n    \n    // Check if user is active\n    if (!user.isActive) {\n      return NextResponse.json(\n        { error: 'Account is deactivated' },\n        { status: 401 }\n      );\n    }\n    \n    // Generate JWT token\n    const token = jwt.sign(\n      { \n        userId: user._id,\n        email: user.email,\n        role: user.role \n      },\n      process.env.JWT_SECRET!,\n      { expiresIn: '7d' }\n    );\n    \n    // Create response\n    const response = NextResponse.json({\n      message: 'Login successful',\n      user: {\n        id: user._id,\n        email: user.email,\n        name: user.name,\n        role: user.role\n      }\n    });\n    \n    // Set HTTP-only cookie\n    response.cookies.set('auth-token', token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'strict',\n      maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days\n    });\n    \n    return response;\n    \n  } catch (error) {\n    console.error('Login error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9C,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,MAAM,WAAW;QAAG;QAC7D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,kBAAkB,MAAM,KAAK,eAAe,CAAC;QACnD,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CACpB;YACE,QAAQ,KAAK,GAAG;YAChB,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;QACjB,GACA,QAAQ,GAAG,CAAC,UAAU,EACtB;YAAE,WAAW;QAAK;QAGpB,kBAAkB;QAClB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;YACT,MAAM;gBACJ,IAAI,KAAK,GAAG;gBACZ,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;YACjB;QACF;QAEA,uBAAuB;QACvB,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO;YACxC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,IAAI,KAAK,KAAK,KAAK,KAAK,SAAS;QAC3C;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}