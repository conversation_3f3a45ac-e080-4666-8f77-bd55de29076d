{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached: MongooseCache = global.mongoose || { conn: null, promise: null };\n\nif (!global.mongoose) {\n  global.mongoose = cached;\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAWA,IAAI,SAAwB,OAAO,QAAQ,IAAI;IAAE,MAAM;IAAM,SAAS;AAAK;AAE3E,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;AACpB;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa;IACjD;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/models/Hostel.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IPhoto {\n  url: string;\n  alt: string;\n  category: 'main' | '1-seater-ac' | '1-seater-non-ac' | '2-seater-ac' | '2-seater-non-ac' | \n           '3-seater-ac' | '3-seater-non-ac' | '4-seater-ac' | '4-seater-non-ac' | \n           'common-area' | 'kitchen' | 'bathroom' | 'exterior';\n}\n\nexport interface IPricing {\n  '1-seater': {\n    ac: number;\n    nonAc: number;\n  };\n  '2-seater': {\n    ac: number;\n    nonAc: number;\n  };\n  '3-seater': {\n    ac: number;\n    nonAc: number;\n  };\n  '4-seater': {\n    ac: number;\n    nonAc: number;\n  };\n}\n\nexport interface IRoomSizes {\n  '1-seater': string;\n  '2-seater': string;\n  '3-seater': string;\n  '4-seater': string;\n}\n\nexport interface IFoodMenu {\n  category: string;\n  items: {\n    name: string;\n    price: number;\n    description?: string;\n  }[];\n}\n\nexport interface ICaretaker {\n  name: string;\n  phone: string;\n  availability: string;\n}\n\nexport interface IHostel extends Document {\n  name: string;\n  headline: string;\n  description: string;\n  address: {\n    street: string;\n    city: string;\n    state: string;\n    pincode: string;\n    coordinates: {\n      lat: number;\n      lng: number;\n    };\n  };\n  photos: IPhoto[];\n  pricing: IPricing;\n  roomSizes: IRoomSizes;\n  \n  // Amenities\n  amenities: {\n    wifi: boolean;\n    parking: boolean;\n    laundry: boolean;\n    meals: boolean;\n    ac: boolean;\n    powerBackup: boolean;\n    studyRoom: boolean;\n    commonRoom: boolean;\n    gym: boolean;\n    indoorGames: boolean;\n    outdoorGames: boolean;\n    library: boolean;\n    medical: boolean;\n    security: boolean;\n    cctv: boolean;\n    biometric: boolean;\n    other: string[];\n  };\n  \n  // Location details\n  nearbyLandmarks: string[];\n  nearestColleges: {\n    name: string;\n    distance: string;\n  }[];\n  \n  // Food and dining\n  foodMenu: IFoodMenu[];\n  collegeLunchProvision: boolean;\n  \n  // Security and rules\n  securityFeatures: {\n    cctv: boolean;\n    securityGuard: boolean;\n    biometricAccess: boolean;\n    timeRestrictions: {\n      enabled: boolean;\n      inTime: string;\n      outTime: string;\n    };\n  };\n  \n  // Staff\n  caretaker: ICaretaker;\n  \n  // Facilities\n  gymFacilities: string[];\n  indoorSportsFacilities: string[];\n  \n  // Events and activities\n  eventsAndFests: {\n    name: string;\n    description: string;\n    frequency: string;\n  }[];\n  \n  // Travel facilities\n  travelFacilities: string[];\n  \n  // Contact information\n  owner: {\n    name: string;\n    phone: string;\n    email?: string;\n  };\n  \n  // Metadata\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst PhotoSchema = new Schema<IPhoto>({\n  url: { type: String, required: true },\n  alt: { type: String, required: true },\n  category: { \n    type: String, \n    required: true,\n    enum: ['main', '1-seater-ac', '1-seater-non-ac', '2-seater-ac', '2-seater-non-ac', \n           '3-seater-ac', '3-seater-non-ac', '4-seater-ac', '4-seater-non-ac', \n           'common-area', 'kitchen', 'bathroom', 'exterior']\n  }\n});\n\nconst PricingSchema = new Schema<IPricing>({\n  '1-seater': {\n    ac: { type: Number, required: true },\n    nonAc: { type: Number, required: true }\n  },\n  '2-seater': {\n    ac: { type: Number, required: true },\n    nonAc: { type: Number, required: true }\n  },\n  '3-seater': {\n    ac: { type: Number, required: true },\n    nonAc: { type: Number, required: true }\n  },\n  '4-seater': {\n    ac: { type: Number, required: true },\n    nonAc: { type: Number, required: true }\n  }\n});\n\nconst HostelSchema = new Schema<IHostel>({\n  name: { type: String, required: true },\n  headline: { type: String, required: true },\n  description: { type: String, required: true },\n  \n  address: {\n    street: { type: String, required: true },\n    city: { type: String, required: true },\n    state: { type: String, required: true },\n    pincode: { type: String, required: true },\n    coordinates: {\n      lat: { type: Number, required: true },\n      lng: { type: Number, required: true }\n    }\n  },\n  \n  photos: [PhotoSchema],\n  pricing: { type: PricingSchema, required: true },\n  \n  roomSizes: {\n    '1-seater': { type: String, required: true },\n    '2-seater': { type: String, required: true },\n    '3-seater': { type: String, required: true },\n    '4-seater': { type: String, required: true }\n  },\n  \n  amenities: {\n    wifi: { type: Boolean, default: false },\n    parking: { type: Boolean, default: false },\n    laundry: { type: Boolean, default: false },\n    meals: { type: Boolean, default: false },\n    ac: { type: Boolean, default: false },\n    powerBackup: { type: Boolean, default: false },\n    studyRoom: { type: Boolean, default: false },\n    commonRoom: { type: Boolean, default: false },\n    gym: { type: Boolean, default: false },\n    indoorGames: { type: Boolean, default: false },\n    outdoorGames: { type: Boolean, default: false },\n    library: { type: Boolean, default: false },\n    medical: { type: Boolean, default: false },\n    security: { type: Boolean, default: false },\n    cctv: { type: Boolean, default: false },\n    biometric: { type: Boolean, default: false },\n    other: [{ type: String }]\n  },\n  \n  nearbyLandmarks: [{ type: String }],\n  nearestColleges: [{\n    name: { type: String, required: true },\n    distance: { type: String, required: true }\n  }],\n  \n  foodMenu: [{\n    category: { type: String, required: true },\n    items: [{\n      name: { type: String, required: true },\n      price: { type: Number, required: true },\n      description: { type: String }\n    }]\n  }],\n  \n  collegeLunchProvision: { type: Boolean, default: false },\n  \n  securityFeatures: {\n    cctv: { type: Boolean, default: false },\n    securityGuard: { type: Boolean, default: false },\n    biometricAccess: { type: Boolean, default: false },\n    timeRestrictions: {\n      enabled: { type: Boolean, default: false },\n      inTime: { type: String },\n      outTime: { type: String }\n    }\n  },\n  \n  caretaker: {\n    name: { type: String, required: true },\n    phone: { type: String, required: true },\n    availability: { type: String, required: true }\n  },\n  \n  gymFacilities: [{ type: String }],\n  indoorSportsFacilities: [{ type: String }],\n  \n  eventsAndFests: [{\n    name: { type: String, required: true },\n    description: { type: String, required: true },\n    frequency: { type: String, required: true }\n  }],\n  \n  travelFacilities: [{ type: String }],\n  \n  owner: {\n    name: { type: String, required: true },\n    phone: { type: String, required: true },\n    email: { type: String }\n  },\n  \n  isActive: { type: Boolean, default: true }\n}, {\n  timestamps: true\n});\n\n// Create indexes for better query performance\nHostelSchema.index({ 'address.coordinates': '2dsphere' });\nHostelSchema.index({ name: 'text', headline: 'text', description: 'text' });\nHostelSchema.index({ isActive: 1 });\n\nexport default mongoose.models.Hostel || mongoose.model<IHostel>('Hostel', HostelSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA+IA,MAAM,cAAc,IAAI,yGAAA,CAAA,SAAM,CAAS;IACrC,KAAK;QAAE,MAAM;QAAQ,UAAU;IAAK;IACpC,KAAK;QAAE,MAAM;QAAQ,UAAU;IAAK;IACpC,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;YAAe;YAAmB;YAAe;YACzD;YAAe;YAAmB;YAAe;YACjD;YAAe;YAAW;YAAY;SAAW;IAC1D;AACF;AAEA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAW;IACzC,YAAY;QACV,IAAI;YAAE,MAAM;YAAQ,UAAU;QAAK;QACnC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;IACA,YAAY;QACV,IAAI;YAAE,MAAM;YAAQ,UAAU;QAAK;QACnC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;IACA,YAAY;QACV,IAAI;YAAE,MAAM;YAAQ,UAAU;QAAK;QACnC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;IACA,YAAY;QACV,IAAI;YAAE,MAAM;YAAQ,UAAU;QAAK;QACnC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAU;IACvC,MAAM;QAAE,MAAM;QAAQ,UAAU;IAAK;IACrC,UAAU;QAAE,MAAM;QAAQ,UAAU;IAAK;IACzC,aAAa;QAAE,MAAM;QAAQ,UAAU;IAAK;IAE5C,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,UAAU;QAAK;QACvC,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;QACtC,SAAS;YAAE,MAAM;YAAQ,UAAU;QAAK;QACxC,aAAa;YACX,KAAK;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACpC,KAAK;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACtC;IACF;IAEA,QAAQ;QAAC;KAAY;IACrB,SAAS;QAAE,MAAM;QAAe,UAAU;IAAK;IAE/C,WAAW;QACT,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC3C,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC3C,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC3C,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC7C;IAEA,WAAW;QACT,MAAM;YAAE,MAAM;YAAS,SAAS;QAAM;QACtC,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,OAAO;YAAE,MAAM;YAAS,SAAS;QAAM;QACvC,IAAI;YAAE,MAAM;YAAS,SAAS;QAAM;QACpC,aAAa;YAAE,MAAM;YAAS,SAAS;QAAM;QAC7C,WAAW;YAAE,MAAM;YAAS,SAAS;QAAM;QAC3C,YAAY;YAAE,MAAM;YAAS,SAAS;QAAM;QAC5C,KAAK;YAAE,MAAM;YAAS,SAAS;QAAM;QACrC,aAAa;YAAE,MAAM;YAAS,SAAS;QAAM;QAC7C,cAAc;YAAE,MAAM;YAAS,SAAS;QAAM;QAC9C,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,UAAU;YAAE,MAAM;YAAS,SAAS;QAAM;QAC1C,MAAM;YAAE,MAAM;YAAS,SAAS;QAAM;QACtC,WAAW;YAAE,MAAM;YAAS,SAAS;QAAM;QAC3C,OAAO;YAAC;gBAAE,MAAM;YAAO;SAAE;IAC3B;IAEA,iBAAiB;QAAC;YAAE,MAAM;QAAO;KAAE;IACnC,iBAAiB;QAAC;YAChB,MAAM;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACrC,UAAU;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QAC3C;KAAE;IAEF,UAAU;QAAC;YACT,UAAU;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACzC,OAAO;gBAAC;oBACN,MAAM;wBAAE,MAAM;wBAAQ,UAAU;oBAAK;oBACrC,OAAO;wBAAE,MAAM;wBAAQ,UAAU;oBAAK;oBACtC,aAAa;wBAAE,MAAM;oBAAO;gBAC9B;aAAE;QACJ;KAAE;IAEF,uBAAuB;QAAE,MAAM;QAAS,SAAS;IAAM;IAEvD,kBAAkB;QAChB,MAAM;YAAE,MAAM;YAAS,SAAS;QAAM;QACtC,eAAe;YAAE,MAAM;YAAS,SAAS;QAAM;QAC/C,iBAAiB;YAAE,MAAM;YAAS,SAAS;QAAM;QACjD,kBAAkB;YAChB,SAAS;gBAAE,MAAM;gBAAS,SAAS;YAAM;YACzC,QAAQ;gBAAE,MAAM;YAAO;YACvB,SAAS;gBAAE,MAAM;YAAO;QAC1B;IACF;IAEA,WAAW;QACT,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;QACtC,cAAc;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC/C;IAEA,eAAe;QAAC;YAAE,MAAM;QAAO;KAAE;IACjC,wBAAwB;QAAC;YAAE,MAAM;QAAO;KAAE;IAE1C,gBAAgB;QAAC;YACf,MAAM;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACrC,aAAa;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YAC5C,WAAW;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QAC5C;KAAE;IAEF,kBAAkB;QAAC;YAAE,MAAM;QAAO;KAAE;IAEpC,OAAO;QACL,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;QACtC,OAAO;YAAE,MAAM;QAAO;IACxB;IAEA,UAAU;QAAE,MAAM;QAAS,SAAS;IAAK;AAC3C,GAAG;IACD,YAAY;AACd;AAEA,8CAA8C;AAC9C,aAAa,KAAK,CAAC;IAAE,uBAAuB;AAAW;AACvD,aAAa,KAAK,CAAC;IAAE,MAAM;IAAQ,UAAU;IAAQ,aAAa;AAAO;AACzE,aAAa,KAAK,CAAC;IAAE,UAAU;AAAE;uCAElB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Calculate distance between two coordinates using Haversine formula\nexport function calculateDistance(\n  lat1: number,\n  lon1: number,\n  lat2: number,\n  lon2: number\n): number {\n  const R = 6371; // Radius of the Earth in kilometers\n  const dLat = deg2rad(lat2 - lat1);\n  const dLon = deg2rad(lon2 - lon1);\n  const a =\n    Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *\n    Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  const d = R * c; // Distance in kilometers\n  return Math.round(d * 100) / 100; // Round to 2 decimal places\n}\n\nfunction deg2rad(deg: number): number {\n  return deg * (Math.PI / 180);\n}\n\n// Format price range for display\nexport function formatPriceRange(pricing: any, seaterType?: string): string {\n  if (seaterType) {\n    const seaterPricing = pricing[seaterType];\n    if (seaterPricing) {\n      const min = Math.min(seaterPricing.ac, seaterPricing.nonAc);\n      const max = Math.max(seaterPricing.ac, seaterPricing.nonAc);\n      return min === max ? `₹${min}` : `₹${min} - ₹${max}`;\n    }\n  }\n\n  // Get all prices and find min/max\n  const allPrices: number[] = [];\n  Object.values(pricing).forEach((seater: any) => {\n    allPrices.push(seater.ac, seater.nonAc);\n  });\n\n  const min = Math.min(...allPrices);\n  const max = Math.max(...allPrices);\n\n  return min === max ? `₹${min}` : `₹${min} - ₹${max}`;\n}\n\n// Get user's current location\nexport function getCurrentLocation(): Promise<GeolocationPosition> {\n  return new Promise((resolve, reject) => {\n    if (!navigator.geolocation) {\n      reject(new Error('Geolocation is not supported by this browser.'));\n      return;\n    }\n\n    navigator.geolocation.getCurrentPosition(\n      (position) => resolve(position),\n      (error) => reject(error),\n      {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 300000 // 5 minutes\n      }\n    );\n  });\n}\n\n// Format distance for display\nexport function formatDistance(distance: number): string {\n  if (distance < 1) {\n    return `${Math.round(distance * 1000)}m`;\n  }\n  return `${distance}km`;\n}\n\n// Debounce function for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Validate email format\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// Validate phone number (Indian format)\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[6-9]\\d{9}$/;\n  return phoneRegex.test(phone.replace(/\\D/g, ''));\n}\n\n// Generate slug from string\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim();\n}\n\n// Format currency\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(amount);\n}\n\n// Truncate text\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).trim() + '...';\n}\n\n// Sort hostels by distance\nexport function sortHostelsByDistance(\n  hostels: any[],\n  userLat?: number,\n  userLng?: number\n): any[] {\n  if (!userLat || !userLng) return hostels;\n\n  return hostels\n    .map(hostel => ({\n      ...hostel,\n      distance: calculateDistance(\n        userLat,\n        userLng,\n        hostel.address.coordinates.lat,\n        hostel.address.coordinates.lng\n      )\n    }))\n    .sort((a, b) => a.distance - b.distance);\n}\n\n// Filter hostels based on criteria\nexport function filterHostels(\n  hostels: any[],\n  filters: {\n    seaterType?: string;\n    acType?: string;\n    priceRange?: [number, number];\n    amenities?: string[];\n    maxDistance?: number;\n    userLocation?: { lat: number; lng: number };\n  }\n): any[] {\n  return hostels.filter(hostel => {\n    // Seater type filter\n    if (filters.seaterType && filters.seaterType !== 'all') {\n      const seaterPricing = hostel.pricing[filters.seaterType];\n      if (!seaterPricing) return false;\n    }\n\n    // Price range filter\n    if (filters.priceRange) {\n      const [minPrice, maxPrice] = filters.priceRange;\n      const hostelPrices: number[] = [];\n\n      if (filters.seaterType && filters.seaterType !== 'all') {\n        const seaterPricing = hostel.pricing[filters.seaterType];\n        if (filters.acType === 'ac') {\n          hostelPrices.push(seaterPricing.ac);\n        } else if (filters.acType === 'non-ac') {\n          hostelPrices.push(seaterPricing.nonAc);\n        } else {\n          hostelPrices.push(seaterPricing.ac, seaterPricing.nonAc);\n        }\n      } else {\n        Object.values(hostel.pricing).forEach((seater: any) => {\n          if (filters.acType === 'ac') {\n            hostelPrices.push(seater.ac);\n          } else if (filters.acType === 'non-ac') {\n            hostelPrices.push(seater.nonAc);\n          } else {\n            hostelPrices.push(seater.ac, seater.nonAc);\n          }\n        });\n      }\n\n      const hostelMinPrice = Math.min(...hostelPrices);\n      const hostelMaxPrice = Math.max(...hostelPrices);\n\n      if (hostelMaxPrice < minPrice || hostelMinPrice > maxPrice) {\n        return false;\n      }\n    }\n\n    // Amenities filter\n    if (filters.amenities && filters.amenities.length > 0) {\n      const hasAllAmenities = filters.amenities.every(amenity => {\n        return hostel.amenities[amenity] === true;\n      });\n      if (!hasAllAmenities) return false;\n    }\n\n    // Distance filter\n    if (filters.maxDistance && filters.userLocation) {\n      const distance = calculateDistance(\n        filters.userLocation.lat,\n        filters.userLocation.lng,\n        hostel.address.coordinates.lat,\n        hostel.address.coordinates.lng\n      );\n      if (distance > filters.maxDistance) return false;\n    }\n\n    return true;\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,kBACd,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,IAAY;IAEZ,MAAM,IAAI,MAAM,oCAAoC;IACpD,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,IACJ,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO,KACrC,KAAK,GAAG,CAAC,QAAQ,SAAS,KAAK,GAAG,CAAC,QAAQ,SAC3C,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO;IACvC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;IACrD,MAAM,IAAI,IAAI,GAAG,yBAAyB;IAC1C,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO,KAAK,4BAA4B;AAChE;AAEA,SAAS,QAAQ,GAAW;IAC1B,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG;AAC7B;AAGO,SAAS,iBAAiB,OAAY,EAAE,UAAmB;IAChE,IAAI,YAAY;QACd,MAAM,gBAAgB,OAAO,CAAC,WAAW;QACzC,IAAI,eAAe;YACjB,MAAM,MAAM,KAAK,GAAG,CAAC,cAAc,EAAE,EAAE,cAAc,KAAK;YAC1D,MAAM,MAAM,KAAK,GAAG,CAAC,cAAc,EAAE,EAAE,cAAc,KAAK;YAC1D,OAAO,QAAQ,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,KAAK;QACtD;IACF;IAEA,kCAAkC;IAClC,MAAM,YAAsB,EAAE;IAC9B,OAAO,MAAM,CAAC,SAAS,OAAO,CAAC,CAAC;QAC9B,UAAU,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK;IACxC;IAEA,MAAM,MAAM,KAAK,GAAG,IAAI;IACxB,MAAM,MAAM,KAAK,GAAG,IAAI;IAExB,OAAO,QAAQ,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,KAAK;AACtD;AAGO,SAAS;IACd,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,OAAO,IAAI,MAAM;YACjB;QACF;QAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC,WAAa,QAAQ,WACtB,CAAC,QAAU,OAAO,QAClB;YACE,oBAAoB;YACpB,SAAS;YACT,YAAY,OAAO,YAAY;QACjC;IAEJ;AACF;AAGO,SAAS,eAAe,QAAgB;IAC7C,IAAI,WAAW,GAAG;QAChB,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,MAAM,CAAC,CAAC;IAC1C;IACA,OAAO,GAAG,SAAS,EAAE,CAAC;AACxB;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAGO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;AAC/C;AAGO,SAAS,sBACd,OAAc,EACd,OAAgB,EAChB,OAAgB;IAEhB,IAAI,CAAC,WAAW,CAAC,SAAS,OAAO;IAEjC,OAAO,QACJ,GAAG,CAAC,CAAA,SAAU,CAAC;YACd,GAAG,MAAM;YACT,UAAU,kBACR,SACA,SACA,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG,EAC9B,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG;QAElC,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;AAC3C;AAGO,SAAS,cACd,OAAc,EACd,OAOC;IAED,OAAO,QAAQ,MAAM,CAAC,CAAA;QACpB,qBAAqB;QACrB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,KAAK,OAAO;YACtD,MAAM,gBAAgB,OAAO,OAAO,CAAC,QAAQ,UAAU,CAAC;YACxD,IAAI,CAAC,eAAe,OAAO;QAC7B;QAEA,qBAAqB;QACrB,IAAI,QAAQ,UAAU,EAAE;YACtB,MAAM,CAAC,UAAU,SAAS,GAAG,QAAQ,UAAU;YAC/C,MAAM,eAAyB,EAAE;YAEjC,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,KAAK,OAAO;gBACtD,MAAM,gBAAgB,OAAO,OAAO,CAAC,QAAQ,UAAU,CAAC;gBACxD,IAAI,QAAQ,MAAM,KAAK,MAAM;oBAC3B,aAAa,IAAI,CAAC,cAAc,EAAE;gBACpC,OAAO,IAAI,QAAQ,MAAM,KAAK,UAAU;oBACtC,aAAa,IAAI,CAAC,cAAc,KAAK;gBACvC,OAAO;oBACL,aAAa,IAAI,CAAC,cAAc,EAAE,EAAE,cAAc,KAAK;gBACzD;YACF,OAAO;gBACL,OAAO,MAAM,CAAC,OAAO,OAAO,EAAE,OAAO,CAAC,CAAC;oBACrC,IAAI,QAAQ,MAAM,KAAK,MAAM;wBAC3B,aAAa,IAAI,CAAC,OAAO,EAAE;oBAC7B,OAAO,IAAI,QAAQ,MAAM,KAAK,UAAU;wBACtC,aAAa,IAAI,CAAC,OAAO,KAAK;oBAChC,OAAO;wBACL,aAAa,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK;oBAC3C;gBACF;YACF;YAEA,MAAM,iBAAiB,KAAK,GAAG,IAAI;YACnC,MAAM,iBAAiB,KAAK,GAAG,IAAI;YAEnC,IAAI,iBAAiB,YAAY,iBAAiB,UAAU;gBAC1D,OAAO;YACT;QACF;QAEA,mBAAmB;QACnB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;YACrD,MAAM,kBAAkB,QAAQ,SAAS,CAAC,KAAK,CAAC,CAAA;gBAC9C,OAAO,OAAO,SAAS,CAAC,QAAQ,KAAK;YACvC;YACA,IAAI,CAAC,iBAAiB,OAAO;QAC/B;QAEA,kBAAkB;QAClB,IAAI,QAAQ,WAAW,IAAI,QAAQ,YAAY,EAAE;YAC/C,MAAM,WAAW,kBACf,QAAQ,YAAY,CAAC,GAAG,EACxB,QAAQ,YAAY,CAAC,GAAG,EACxB,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG,EAC9B,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG;YAEhC,IAAI,WAAW,QAAQ,WAAW,EAAE,OAAO;QAC7C;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/app/api/hostels/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Hostel from '@/models/Hostel';\nimport { filterHostels, sortHostelsByDistance } from '@/lib/utils';\n\nexport async function GET(request: NextRequest) {\n  try {\n    await connectDB();\n    \n    const { searchParams } = new URL(request.url);\n    \n    // Get query parameters\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '12');\n    const search = searchParams.get('search') || '';\n    const seaterType = searchParams.get('seaterType') || '';\n    const acType = searchParams.get('acType') || '';\n    const minPrice = searchParams.get('minPrice');\n    const maxPrice = searchParams.get('maxPrice');\n    const amenities = searchParams.get('amenities')?.split(',').filter(Boolean) || [];\n    const sortBy = searchParams.get('sortBy') || 'name';\n    const sortOrder = searchParams.get('sortOrder') || 'asc';\n    const userLat = searchParams.get('userLat');\n    const userLng = searchParams.get('userLng');\n    const maxDistance = searchParams.get('maxDistance');\n    \n    // Build query\n    let query: any = { isActive: true };\n    \n    // Search functionality\n    if (search) {\n      query.$or = [\n        { name: { $regex: search, $options: 'i' } },\n        { headline: { $regex: search, $options: 'i' } },\n        { description: { $regex: search, $options: 'i' } },\n        { 'address.city': { $regex: search, $options: 'i' } },\n        { 'address.state': { $regex: search, $options: 'i' } }\n      ];\n    }\n    \n    // Get all hostels first\n    let hostels = await Hostel.find(query).lean();\n    \n    // Apply filters\n    const filters: any = {};\n    \n    if (seaterType && seaterType !== 'all') {\n      filters.seaterType = seaterType;\n    }\n    \n    if (acType && acType !== 'all') {\n      filters.acType = acType;\n    }\n    \n    if (minPrice && maxPrice) {\n      filters.priceRange = [parseInt(minPrice), parseInt(maxPrice)];\n    }\n    \n    if (amenities.length > 0) {\n      filters.amenities = amenities;\n    }\n    \n    if (maxDistance && userLat && userLng) {\n      filters.maxDistance = parseFloat(maxDistance);\n      filters.userLocation = {\n        lat: parseFloat(userLat),\n        lng: parseFloat(userLng)\n      };\n    }\n    \n    // Apply filters\n    hostels = filterHostels(hostels, filters);\n    \n    // Sort hostels\n    if (sortBy === 'distance' && userLat && userLng) {\n      hostels = sortHostelsByDistance(\n        hostels,\n        parseFloat(userLat),\n        parseFloat(userLng)\n      );\n    } else {\n      hostels.sort((a, b) => {\n        let aValue, bValue;\n        \n        switch (sortBy) {\n          case 'price':\n            // Get minimum price for sorting\n            const aPrices = Object.values(a.pricing).flatMap((p: any) => [p.ac, p.nonAc]);\n            const bPrices = Object.values(b.pricing).flatMap((p: any) => [p.ac, p.nonAc]);\n            aValue = Math.min(...aPrices);\n            bValue = Math.min(...bPrices);\n            break;\n          case 'name':\n            aValue = a.name.toLowerCase();\n            bValue = b.name.toLowerCase();\n            break;\n          default:\n            aValue = a[sortBy];\n            bValue = b[sortBy];\n        }\n        \n        if (sortOrder === 'desc') {\n          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n        } else {\n          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n        }\n      });\n    }\n    \n    // Pagination\n    const skip = (page - 1) * limit;\n    const paginatedHostels = hostels.slice(skip, skip + limit);\n    const totalHostels = hostels.length;\n    const totalPages = Math.ceil(totalHostels / limit);\n    \n    return NextResponse.json({\n      hostels: paginatedHostels,\n      pagination: {\n        currentPage: page,\n        totalPages,\n        totalHostels,\n        hasNextPage: page < totalPages,\n        hasPrevPage: page > 1\n      }\n    });\n    \n  } catch (error) {\n    console.error('Get hostels error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Check authentication\n    const token = request.cookies.get('auth-token')?.value;\n    if (!token) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n    \n    await connectDB();\n    \n    const hostelData = await request.json();\n    \n    // Create new hostel\n    const hostel = new Hostel(hostelData);\n    await hostel.save();\n    \n    return NextResponse.json({\n      message: 'Hostel created successfully',\n      hostel\n    }, { status: 201 });\n    \n  } catch (error) {\n    console.error('Create hostel error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,uBAAuB;QACvB,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,aAAa,aAAa,GAAG,CAAC,iBAAiB;QACrD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC,cAAc,MAAM,KAAK,OAAO,YAAY,EAAE;QACjF,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QACnD,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,cAAc,aAAa,GAAG,CAAC;QAErC,cAAc;QACd,IAAI,QAAa;YAAE,UAAU;QAAK;QAElC,uBAAuB;QACvB,IAAI,QAAQ;YACV,MAAM,GAAG,GAAG;gBACV;oBAAE,MAAM;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC1C;oBAAE,UAAU;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC9C;oBAAE,aAAa;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBACjD;oBAAE,gBAAgB;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBACpD;oBAAE,iBAAiB;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;aACtD;QACH;QAEA,wBAAwB;QACxB,IAAI,UAAU,MAAM,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,OAAO,IAAI;QAE3C,gBAAgB;QAChB,MAAM,UAAe,CAAC;QAEtB,IAAI,cAAc,eAAe,OAAO;YACtC,QAAQ,UAAU,GAAG;QACvB;QAEA,IAAI,UAAU,WAAW,OAAO;YAC9B,QAAQ,MAAM,GAAG;QACnB;QAEA,IAAI,YAAY,UAAU;YACxB,QAAQ,UAAU,GAAG;gBAAC,SAAS;gBAAW,SAAS;aAAU;QAC/D;QAEA,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,QAAQ,SAAS,GAAG;QACtB;QAEA,IAAI,eAAe,WAAW,SAAS;YACrC,QAAQ,WAAW,GAAG,WAAW;YACjC,QAAQ,YAAY,GAAG;gBACrB,KAAK,WAAW;gBAChB,KAAK,WAAW;YAClB;QACF;QAEA,gBAAgB;QAChB,UAAU,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;QAEjC,eAAe;QACf,IAAI,WAAW,cAAc,WAAW,SAAS;YAC/C,UAAU,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,EAC5B,SACA,WAAW,UACX,WAAW;QAEf,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,GAAG;gBACf,IAAI,QAAQ;gBAEZ,OAAQ;oBACN,KAAK;wBACH,gCAAgC;wBAChC,MAAM,UAAU,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,IAAW;gCAAC,EAAE,EAAE;gCAAE,EAAE,KAAK;6BAAC;wBAC5E,MAAM,UAAU,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,IAAW;gCAAC,EAAE,EAAE;gCAAE,EAAE,KAAK;6BAAC;wBAC5E,SAAS,KAAK,GAAG,IAAI;wBACrB,SAAS,KAAK,GAAG,IAAI;wBACrB;oBACF,KAAK;wBACH,SAAS,EAAE,IAAI,CAAC,WAAW;wBAC3B,SAAS,EAAE,IAAI,CAAC,WAAW;wBAC3B;oBACF;wBACE,SAAS,CAAC,CAAC,OAAO;wBAClB,SAAS,CAAC,CAAC,OAAO;gBACtB;gBAEA,IAAI,cAAc,QAAQ;oBACxB,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;gBACtD,OAAO;oBACL,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;gBACtD;YACF;QACF;QAEA,aAAa;QACb,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,mBAAmB,QAAQ,KAAK,CAAC,MAAM,OAAO;QACpD,MAAM,eAAe,QAAQ,MAAM;QACnC,MAAM,aAAa,KAAK,IAAI,CAAC,eAAe;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,YAAY;gBACV,aAAa;gBACb;gBACA;gBACA,aAAa,OAAO;gBACpB,aAAa,OAAO;YACtB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,uBAAuB;QACvB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QACjD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,aAAa,MAAM,QAAQ,IAAI;QAErC,oBAAoB;QACpB,MAAM,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;QAC1B,MAAM,OAAO,IAAI;QAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}