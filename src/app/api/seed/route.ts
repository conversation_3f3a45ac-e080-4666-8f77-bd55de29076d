import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import Hostel from '@/models/Hostel';

export async function POST() {
  try {
    await connectDB();
    
    // Create admin user
    const adminExists = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminExists) {
      const admin = new User({
        email: '<EMAIL>',
        password: 'nk10nikhil',
        name: 'Admin User',
        role: 'admin'
      });
      
      await admin.save();
      console.log('Admin user created');
    }
    
    // Create sample hostels
    const hostelCount = await Hostel.countDocuments();
    
    if (hostelCount === 0) {
      const sampleHostels = [
        {
          name: "Green Valley Hostel",
          headline: "Premium accommodation with modern amenities",
          description: "A well-maintained hostel offering comfortable living spaces with all modern amenities. Located in a peaceful environment perfect for students.",
          address: {
            street: "123 College Road",
            city: "Bangalore",
            state: "Karnataka",
            pincode: "560001",
            coordinates: {
              lat: 12.9716,
              lng: 77.5946
            }
          },
          photos: [
            {
              url: "/api/placeholder/800/600",
              alt: "Green Valley Hostel Main Building",
              category: "main"
            },
            {
              url: "/api/placeholder/600/400",
              alt: "Single Seater AC Room",
              category: "1-seater-ac"
            },
            {
              url: "/api/placeholder/600/400",
              alt: "Double Seater Non-AC Room",
              category: "2-seater-non-ac"
            }
          ],
          pricing: {
            "1-seater": { ac: 15000, nonAc: 12000 },
            "2-seater": { ac: 10000, nonAc: 8000 },
            "3-seater": { ac: 8000, nonAc: 6500 },
            "4-seater": { ac: 7000, nonAc: 5500 }
          },
          roomSizes: {
            "1-seater": "12x10 ft",
            "2-seater": "14x12 ft",
            "3-seater": "16x14 ft",
            "4-seater": "18x16 ft"
          },
          amenities: {
            wifi: true,
            parking: true,
            laundry: true,
            meals: true,
            ac: true,
            powerBackup: true,
            studyRoom: true,
            commonRoom: true,
            gym: true,
            indoorGames: true,
            outdoorGames: false,
            library: true,
            medical: true,
            security: true,
            cctv: true,
            biometric: true,
            other: ["Water Purifier", "Refrigerator"]
          },
          nearbyLandmarks: ["City Mall", "Central Park", "Metro Station"],
          nearestColleges: [
            { name: "ABC Engineering College", distance: "2 km" },
            { name: "XYZ Medical College", distance: "3.5 km" }
          ],
          foodMenu: [
            {
              category: "Breakfast",
              items: [
                { name: "Idli Sambar", price: 40, description: "South Indian breakfast" },
                { name: "Poha", price: 35, description: "Maharashtrian breakfast" }
              ]
            },
            {
              category: "Lunch",
              items: [
                { name: "Veg Thali", price: 80, description: "Complete vegetarian meal" },
                { name: "Non-Veg Thali", price: 120, description: "Complete non-vegetarian meal" }
              ]
            }
          ],
          collegeLunchProvision: true,
          securityFeatures: {
            cctv: true,
            securityGuard: true,
            biometricAccess: true,
            timeRestrictions: {
              enabled: true,
              inTime: "10:00 PM",
              outTime: "6:00 AM"
            }
          },
          caretaker: {
            name: "Ramesh Kumar",
            phone: "9876543210",
            availability: "24/7"
          },
          gymFacilities: ["Treadmill", "Dumbbells", "Bench Press"],
          indoorSportsFacilities: ["Table Tennis", "Carrom", "Chess"],
          eventsAndFests: [
            {
              name: "Annual Sports Day",
              description: "Inter-hostel sports competition",
              frequency: "Yearly"
            }
          ],
          travelFacilities: ["Bus Stop nearby", "Metro connectivity"],
          owner: {
            name: "Suresh Patel",
            phone: "9123456789",
            email: "<EMAIL>"
          }
        },
        {
          name: "Sunrise Residency",
          headline: "Budget-friendly accommodation for students",
          description: "Affordable hostel with basic amenities and good connectivity to major colleges and universities.",
          address: {
            street: "456 University Avenue",
            city: "Pune",
            state: "Maharashtra",
            pincode: "411001",
            coordinates: {
              lat: 18.5204,
              lng: 73.8567
            }
          },
          photos: [
            {
              url: "/api/placeholder/800/600",
              alt: "Sunrise Residency Building",
              category: "main"
            }
          ],
          pricing: {
            "1-seater": { ac: 12000, nonAc: 9000 },
            "2-seater": { ac: 8000, nonAc: 6000 },
            "3-seater": { ac: 6500, nonAc: 5000 },
            "4-seater": { ac: 5500, nonAc: 4500 }
          },
          roomSizes: {
            "1-seater": "10x10 ft",
            "2-seater": "12x12 ft",
            "3-seater": "14x12 ft",
            "4-seater": "16x14 ft"
          },
          amenities: {
            wifi: true,
            parking: false,
            laundry: true,
            meals: true,
            ac: false,
            powerBackup: true,
            studyRoom: false,
            commonRoom: true,
            gym: false,
            indoorGames: true,
            outdoorGames: false,
            library: false,
            medical: false,
            security: true,
            cctv: true,
            biometric: false,
            other: ["Water Purifier"]
          },
          nearbyLandmarks: ["Shopping Complex", "Bus Terminal"],
          nearestColleges: [
            { name: "Pune University", distance: "1.5 km" },
            { name: "Engineering Institute", distance: "2 km" }
          ],
          foodMenu: [
            {
              category: "Breakfast",
              items: [
                { name: "Bread Butter", price: 25 },
                { name: "Tea", price: 10 }
              ]
            }
          ],
          collegeLunchProvision: false,
          securityFeatures: {
            cctv: true,
            securityGuard: false,
            biometricAccess: false,
            timeRestrictions: {
              enabled: true,
              inTime: "11:00 PM",
              outTime: "5:00 AM"
            }
          },
          caretaker: {
            name: "Prakash Singh",
            phone: "9876543211",
            availability: "9 AM - 9 PM"
          },
          gymFacilities: [],
          indoorSportsFacilities: ["Carrom"],
          eventsAndFests: [],
          travelFacilities: ["Bus connectivity"],
          owner: {
            name: "Rajesh Sharma",
            phone: "9123456788"
          }
        }
      ];
      
      await Hostel.insertMany(sampleHostels);
      console.log('Sample hostels created');
    }
    
    return NextResponse.json({
      message: 'Database seeded successfully'
    });
    
  } catch (error) {
    console.error('Seed error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
