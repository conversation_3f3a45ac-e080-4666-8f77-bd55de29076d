{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { \n  HomeIcon, \n  BuildingOfficeIcon, \n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  MagnifyingGlassIcon\n} from '@heroicons/react/24/outline';\nimport toast from 'react-hot-toast';\n\ninterface User {\n  id: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  const checkAuth = async () => {\n    try {\n      const response = await fetch('/api/auth/me');\n      if (response.ok) {\n        const data = await response.json();\n        setUser(data.user);\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      const response = await fetch('/api/auth/logout', {\n        method: 'POST',\n      });\n\n      if (response.ok) {\n        setUser(null);\n        toast.success('Logged out successfully');\n        router.push('/');\n      }\n    } catch (error) {\n      toast.error('Logout failed');\n    }\n  };\n\n  const navigation = [\n    { name: 'Home', href: '/', icon: HomeIcon },\n    { name: 'Hostels', href: '/hostels', icon: BuildingOfficeIcon },\n    { name: 'Compare', href: '/compare', icon: MagnifyingGlassIcon },\n  ];\n\n  return (\n    <header className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <BuildingOfficeIcon className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">MyHostel</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n              >\n                <item.icon className=\"h-4 w-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </nav>\n\n          {/* User Menu */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {!isLoading && (\n              <>\n                {user ? (\n                  <div className=\"flex items-center space-x-4\">\n                    {user.role === 'admin' && (\n                      <Link\n                        href=\"/admin\"\n                        className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors\"\n                      >\n                        Admin Dashboard\n                      </Link>\n                    )}\n                    <div className=\"flex items-center space-x-2\">\n                      <UserIcon className=\"h-5 w-5 text-gray-600\" />\n                      <span className=\"text-sm text-gray-700\">{user.name}</span>\n                    </div>\n                    <button\n                      onClick={handleLogout}\n                      className=\"text-gray-700 hover:text-red-600 text-sm font-medium transition-colors\"\n                    >\n                      Logout\n                    </button>\n                  </div>\n                ) : (\n                  <Link\n                    href=\"/admin/login\"\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors\"\n                  >\n                    Admin Login\n                  </Link>\n                )}\n              </>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 p-2\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <item.icon className=\"h-5 w-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              ))}\n              \n              {!isLoading && (\n                <div className=\"pt-4 border-t border-gray-200 mt-4\">\n                  {user ? (\n                    <div className=\"space-y-2\">\n                      {user.role === 'admin' && (\n                        <Link\n                          href=\"/admin\"\n                          className=\"block bg-blue-600 text-white px-3 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors\"\n                          onClick={() => setIsMenuOpen(false)}\n                        >\n                          Admin Dashboard\n                        </Link>\n                      )}\n                      <div className=\"flex items-center space-x-2 px-3 py-2\">\n                        <UserIcon className=\"h-5 w-5 text-gray-600\" />\n                        <span className=\"text-base text-gray-700\">{user.name}</span>\n                      </div>\n                      <button\n                        onClick={() => {\n                          handleLogout();\n                          setIsMenuOpen(false);\n                        }}\n                        className=\"block w-full text-left text-gray-700 hover:text-red-600 px-3 py-2 text-base font-medium transition-colors\"\n                      >\n                        Logout\n                      </button>\n                    </div>\n                  ) : (\n                    <Link\n                      href=\"/admin/login\"\n                      className=\"block bg-blue-600 text-white px-3 py-2 rounded-md text-base font-medium hover:bg-blue-700 transition-colors\"\n                      onClick={() => setIsMenuOpen(false)}\n                    >\n                      Admin Login\n                    </Link>\n                  )}\n                </div>\n              )}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAbA;;;;;;;AAsBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,KAAK,IAAI;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ;gBACR,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;YAAK,MAAM,+MAAA,CAAA,WAAQ;QAAC;QAC1C;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,mOAAA,CAAA,qBAAkB;QAAC;QAC9D;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,qOAAA,CAAA,sBAAmB;QAAC;KAChE;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,mOAAA,CAAA,qBAAkB;oCAAC,WAAU;;;;;;8CAC9B,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;sCACZ,CAAC,2BACA;0CACG,qBACC,8OAAC;oCAAI,WAAU;;wCACZ,KAAK,IAAI,KAAK,yBACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAIH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAyB,KAAK,IAAI;;;;;;;;;;;;sDAEpD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;yDAKH,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAST,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;;sDAE7B,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCANX,KAAK,IAAI;;;;;4BAUjB,CAAC,2BACA,8OAAC;gCAAI,WAAU;0CACZ,qBACC,8OAAC;oCAAI,WAAU;;wCACZ,KAAK,IAAI,KAAK,yBACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAIH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAA2B,KAAK,IAAI;;;;;;;;;;;;sDAEtD,8OAAC;4CACC,SAAS;gDACP;gDACA,cAAc;4CAChB;4CACA,WAAU;sDACX;;;;;;;;;;;yDAKH,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/app/admin/hostels/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Header from '@/components/Header';\nimport { ArrowLeftIcon } from '@heroicons/react/24/outline';\nimport toast from 'react-hot-toast';\n\nexport default function NewHostelPage() {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    headline: '',\n    description: '',\n    address: {\n      street: '',\n      city: '',\n      state: '',\n      pincode: '',\n      coordinates: {\n        lat: 0,\n        lng: 0\n      }\n    },\n    pricing: {\n      '1-seater': { ac: 0, nonAc: 0 },\n      '2-seater': { ac: 0, nonAc: 0 },\n      '3-seater': { ac: 0, nonAc: 0 },\n      '4-seater': { ac: 0, nonAc: 0 }\n    },\n    roomSizes: {\n      '1-seater': '',\n      '2-seater': '',\n      '3-seater': '',\n      '4-seater': ''\n    },\n    amenities: {\n      wifi: false,\n      parking: false,\n      laundry: false,\n      meals: false,\n      ac: false,\n      powerBackup: false,\n      studyRoom: false,\n      commonRoom: false,\n      gym: false,\n      indoorGames: false,\n      outdoorGames: false,\n      library: false,\n      medical: false,\n      security: false,\n      cctv: false,\n      biometric: false,\n      other: []\n    },\n    nearbyLandmarks: [],\n    nearestColleges: [],\n    foodMenu: [],\n    collegeLunchProvision: false,\n    securityFeatures: {\n      cctv: false,\n      securityGuard: false,\n      biometricAccess: false,\n      timeRestrictions: {\n        enabled: false,\n        inTime: '',\n        outTime: ''\n      }\n    },\n    caretaker: {\n      name: '',\n      phone: '',\n      availability: ''\n    },\n    gymFacilities: [],\n    indoorSportsFacilities: [],\n    eventsAndFests: [],\n    travelFacilities: [],\n    owner: {\n      name: '',\n      phone: '',\n      email: ''\n    },\n    photos: []\n  });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value, type } = e.target;\n    const checked = (e.target as HTMLInputElement).checked;\n\n    if (name.includes('.')) {\n      const keys = name.split('.');\n      setFormData(prev => {\n        const newData = { ...prev };\n        let current: any = newData;\n        \n        for (let i = 0; i < keys.length - 1; i++) {\n          current = current[keys[i]];\n        }\n        \n        current[keys[keys.length - 1]] = type === 'checkbox' ? checked : \n                                        type === 'number' ? parseFloat(value) || 0 : value;\n        return newData;\n      });\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: type === 'checkbox' ? checked : \n                type === 'number' ? parseFloat(value) || 0 : value\n      }));\n    }\n  };\n\n  const handleArrayInputChange = (field: string, index: number, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: prev[field as keyof typeof prev].map((item: any, i: number) => \n        i === index ? value : item\n      )\n    }));\n  };\n\n  const addArrayItem = (field: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: [...prev[field as keyof typeof prev], '']\n    }));\n  };\n\n  const removeArrayItem = (field: string, index: number) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: prev[field as keyof typeof prev].filter((_: any, i: number) => i !== index)\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('/api/hostels', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      if (response.ok) {\n        toast.success('Hostel created successfully!');\n        router.push('/admin');\n      } else {\n        const data = await response.json();\n        toast.error(data.error || 'Failed to create hostel');\n      }\n    } catch (error) {\n      toast.error('An error occurred. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <button\n            onClick={() => router.back()}\n            className=\"flex items-center text-blue-600 hover:text-blue-800 mb-4\"\n          >\n            <ArrowLeftIcon className=\"h-5 w-5 mr-2\" />\n            Back to Dashboard\n          </button>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Add New Hostel</h1>\n          <p className=\"text-gray-600 mt-2\">Fill in the details to add a new hostel to the platform</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* Basic Information */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Basic Information</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Hostel Name *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Headline *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"headline\"\n                  value={formData.headline}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n            </div>\n            \n            <div className=\"mt-4\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Description *\n              </label>\n              <textarea\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                required\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          </div>\n\n          {/* Address */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Address</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Street Address *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"address.street\"\n                  value={formData.address.street}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  City *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"address.city\"\n                  value={formData.address.city}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  State *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"address.state\"\n                  value={formData.address.state}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Pincode *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"address.pincode\"\n                  value={formData.address.pincode}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Latitude *\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"any\"\n                  name=\"address.coordinates.lat\"\n                  value={formData.address.coordinates.lat}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Longitude *\n                </label>\n                <input\n                  type=\"number\"\n                  step=\"any\"\n                  name=\"address.coordinates.lng\"\n                  value={formData.address.coordinates.lng}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Pricing */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Pricing</h2>\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr>\n                    <th className=\"text-left py-2\">Room Type</th>\n                    <th className=\"text-left py-2\">AC Price (₹)</th>\n                    <th className=\"text-left py-2\">Non-AC Price (₹)</th>\n                    <th className=\"text-left py-2\">Room Size</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {Object.keys(formData.pricing).map((type) => (\n                    <tr key={type}>\n                      <td className=\"py-2 font-medium\">{type.replace('-', ' ').toUpperCase()}</td>\n                      <td className=\"py-2\">\n                        <input\n                          type=\"number\"\n                          name={`pricing.${type}.ac`}\n                          value={formData.pricing[type as keyof typeof formData.pricing].ac}\n                          onChange={handleInputChange}\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"py-2\">\n                        <input\n                          type=\"number\"\n                          name={`pricing.${type}.nonAc`}\n                          value={formData.pricing[type as keyof typeof formData.pricing].nonAc}\n                          onChange={handleInputChange}\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"py-2\">\n                        <input\n                          type=\"text\"\n                          name={`roomSizes.${type}`}\n                          value={formData.roomSizes[type as keyof typeof formData.roomSizes]}\n                          onChange={handleInputChange}\n                          placeholder=\"e.g., 12x10 ft\"\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        />\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Contact Information */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Contact Information</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <h3 className=\"text-lg font-medium mb-3\">Owner Details</h3>\n                <div className=\"space-y-3\">\n                  <input\n                    type=\"text\"\n                    name=\"owner.name\"\n                    value={formData.owner.name}\n                    onChange={handleInputChange}\n                    placeholder=\"Owner Name *\"\n                    required\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <input\n                    type=\"tel\"\n                    name=\"owner.phone\"\n                    value={formData.owner.phone}\n                    onChange={handleInputChange}\n                    placeholder=\"Owner Phone *\"\n                    required\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <input\n                    type=\"email\"\n                    name=\"owner.email\"\n                    value={formData.owner.email}\n                    onChange={handleInputChange}\n                    placeholder=\"Owner Email\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n              \n              <div>\n                <h3 className=\"text-lg font-medium mb-3\">Caretaker Details</h3>\n                <div className=\"space-y-3\">\n                  <input\n                    type=\"text\"\n                    name=\"caretaker.name\"\n                    value={formData.caretaker.name}\n                    onChange={handleInputChange}\n                    placeholder=\"Caretaker Name *\"\n                    required\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <input\n                    type=\"tel\"\n                    name=\"caretaker.phone\"\n                    value={formData.caretaker.phone}\n                    onChange={handleInputChange}\n                    placeholder=\"Caretaker Phone *\"\n                    required\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <input\n                    type=\"text\"\n                    name=\"caretaker.availability\"\n                    value={formData.caretaker.availability}\n                    onChange={handleInputChange}\n                    placeholder=\"Availability (e.g., 24/7) *\"\n                    required\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end space-x-4\">\n            <button\n              type=\"button\"\n              onClick={() => router.back()}\n              className=\"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'Creating...' : 'Create Hostel'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,UAAU;QACV,aAAa;QACb,SAAS;YACP,QAAQ;YACR,MAAM;YACN,OAAO;YACP,SAAS;YACT,aAAa;gBACX,KAAK;gBACL,KAAK;YACP;QACF;QACA,SAAS;YACP,YAAY;gBAAE,IAAI;gBAAG,OAAO;YAAE;YAC9B,YAAY;gBAAE,IAAI;gBAAG,OAAO;YAAE;YAC9B,YAAY;gBAAE,IAAI;gBAAG,OAAO;YAAE;YAC9B,YAAY;gBAAE,IAAI;gBAAG,OAAO;YAAE;QAChC;QACA,WAAW;YACT,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;QACA,WAAW;YACT,MAAM;YACN,SAAS;YACT,SAAS;YACT,OAAO;YACP,IAAI;YACJ,aAAa;YACb,WAAW;YACX,YAAY;YACZ,KAAK;YACL,aAAa;YACb,cAAc;YACd,SAAS;YACT,SAAS;YACT,UAAU;YACV,MAAM;YACN,WAAW;YACX,OAAO,EAAE;QACX;QACA,iBAAiB,EAAE;QACnB,iBAAiB,EAAE;QACnB,UAAU,EAAE;QACZ,uBAAuB;QACvB,kBAAkB;YAChB,MAAM;YACN,eAAe;YACf,iBAAiB;YACjB,kBAAkB;gBAChB,SAAS;gBACT,QAAQ;gBACR,SAAS;YACX;QACF;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,cAAc;QAChB;QACA,eAAe,EAAE;QACjB,wBAAwB,EAAE;QAC1B,gBAAgB,EAAE;QAClB,kBAAkB,EAAE;QACpB,OAAO;YACL,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA,QAAQ,EAAE;IACZ;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,MAAM,UAAU,AAAC,EAAE,MAAM,CAAsB,OAAO;QAEtD,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,YAAY,CAAA;gBACV,MAAM,UAAU;oBAAE,GAAG,IAAI;gBAAC;gBAC1B,IAAI,UAAe;gBAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;oBACxC,UAAU,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC5B;gBAEA,OAAO,CAAC,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,SAAS,aAAa,UACvB,SAAS,WAAW,WAAW,UAAU,IAAI;gBAC7E,OAAO;YACT;QACF,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UACtB,SAAS,WAAW,WAAW,UAAU,IAAI;gBACvD,CAAC;QACH;IACF;IAEA,MAAM,yBAAyB,CAAC,OAAe,OAAe;QAC5D,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE,IAAI,CAAC,MAA2B,CAAC,GAAG,CAAC,CAAC,MAAW,IACxD,MAAM,QAAQ,QAAQ;YAE1B,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;uBAAI,IAAI,CAAC,MAA2B;oBAAE;iBAAG;YACpD,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC,OAAe;QACtC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE,IAAI,CAAC,MAA2B,CAAC,MAAM,CAAC,CAAC,GAAQ,IAAc,MAAM;YAChF,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI;gCAC1B,WAAU;;kDAEV,8OAAC,yNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAG5C,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAGpC,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,QAAQ;wDACR,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,QAAQ;wDACR,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,QAAQ;gDACR,MAAM;gDACN,WAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO,CAAC,MAAM;wDAC9B,UAAU;wDACV,QAAQ;wDACR,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO,CAAC,IAAI;wDAC5B,UAAU;wDACV,QAAQ;wDACR,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO,CAAC,KAAK;wDAC7B,UAAU;wDACV,QAAQ;wDACR,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO,CAAC,OAAO;wDAC/B,UAAU;wDACV,QAAQ;wDACR,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO,CAAC,WAAW,CAAC,GAAG;wDACvC,UAAU;wDACV,QAAQ;wDACR,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO,CAAC,WAAW,CAAC,GAAG;wDACvC,UAAU;wDACV,QAAQ;wDACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAOlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;8DACC,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAiB;;;;;;0EAC/B,8OAAC;gEAAG,WAAU;0EAAiB;;;;;;0EAC/B,8OAAC;gEAAG,WAAU;0EAAiB;;;;;;0EAC/B,8OAAC;gEAAG,WAAU;0EAAiB;;;;;;;;;;;;;;;;;8DAGnC,8OAAC;8DACE,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE,GAAG,CAAC,CAAC,qBAClC,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAoB,KAAK,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;8EACpE,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEACC,MAAK;wEACL,MAAM,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;wEAC1B,OAAO,SAAS,OAAO,CAAC,KAAsC,CAAC,EAAE;wEACjE,UAAU;wEACV,WAAU;;;;;;;;;;;8EAGd,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEACC,MAAK;wEACL,MAAM,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC;wEAC7B,OAAO,SAAS,OAAO,CAAC,KAAsC,CAAC,KAAK;wEACpE,UAAU;wEACV,WAAU;;;;;;;;;;;8EAGd,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEACC,MAAK;wEACL,MAAM,CAAC,UAAU,EAAE,MAAM;wEACzB,OAAO,SAAS,SAAS,CAAC,KAAwC;wEAClE,UAAU;wEACV,aAAY;wEACZ,WAAU;;;;;;;;;;;;2DA3BP;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAsCnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK,CAAC,IAAI;gEAC1B,UAAU;gEACV,aAAY;gEACZ,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK,CAAC,KAAK;gEAC3B,UAAU;gEACV,aAAY;gEACZ,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK,CAAC,KAAK;gEAC3B,UAAU;gEACV,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,SAAS,CAAC,IAAI;gEAC9B,UAAU;gEACV,aAAY;gEACZ,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,SAAS,CAAC,KAAK;gEAC/B,UAAU;gEACV,aAAY;gEACZ,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,SAAS,CAAC,YAAY;gEACtC,UAAU;gEACV,aAAY;gEACZ,QAAQ;gEACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}