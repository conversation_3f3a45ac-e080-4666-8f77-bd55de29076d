import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Calculate distance between two coordinates using Haversine formula
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c; // Distance in kilometers
  return Math.round(d * 100) / 100; // Round to 2 decimal places
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}

// Format price range for display
export function formatPriceRange(pricing: any, seaterType?: string): string {
  if (seaterType) {
    const seaterPricing = pricing[seaterType];
    if (seaterPricing) {
      const min = Math.min(seaterPricing.ac, seaterPricing.nonAc);
      const max = Math.max(seaterPricing.ac, seaterPricing.nonAc);
      return min === max ? `₹${min}` : `₹${min} - ₹${max}`;
    }
  }

  // Get all prices and find min/max
  const allPrices: number[] = [];
  Object.values(pricing).forEach((seater: any) => {
    allPrices.push(seater.ac, seater.nonAc);
  });

  const min = Math.min(...allPrices);
  const max = Math.max(...allPrices);

  return min === max ? `₹${min}` : `₹${min} - ₹${max}`;
}

// Get user's current location
export function getCurrentLocation(): Promise<GeolocationPosition> {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser.'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => resolve(position),
      (error) => reject(error),
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  });
}

// Format distance for display
export function formatDistance(distance: number): string {
  if (distance < 1) {
    return `${Math.round(distance * 1000)}m`;
  }
  return `${distance}km`;
}

// Debounce function for search
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate phone number (Indian format)
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[6-9]\d{9}$/;
  return phoneRegex.test(phone.replace(/\D/g, ''));
}

// Generate slug from string
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

// Format currency
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

// Truncate text
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}

// Sort hostels by distance
export function sortHostelsByDistance(
  hostels: any[],
  userLat?: number,
  userLng?: number
): any[] {
  if (!userLat || !userLng) return hostels;

  return hostels
    .map(hostel => ({
      ...hostel,
      distance: calculateDistance(
        userLat,
        userLng,
        hostel.address.coordinates.lat,
        hostel.address.coordinates.lng
      )
    }))
    .sort((a, b) => a.distance - b.distance);
}

// Filter hostels based on criteria
export function filterHostels(
  hostels: any[],
  filters: {
    seaterType?: string;
    acType?: string;
    priceRange?: [number, number];
    amenities?: string[];
    maxDistance?: number;
    userLocation?: { lat: number; lng: number };
  }
): any[] {
  return hostels.filter(hostel => {
    // Seater type filter
    if (filters.seaterType && filters.seaterType !== 'all') {
      const seaterPricing = hostel.pricing[filters.seaterType];
      if (!seaterPricing) return false;
    }

    // Price range filter
    if (filters.priceRange) {
      const [minPrice, maxPrice] = filters.priceRange;
      const hostelPrices: number[] = [];

      if (filters.seaterType && filters.seaterType !== 'all') {
        const seaterPricing = hostel.pricing[filters.seaterType];
        if (filters.acType === 'ac') {
          hostelPrices.push(seaterPricing.ac);
        } else if (filters.acType === 'non-ac') {
          hostelPrices.push(seaterPricing.nonAc);
        } else {
          hostelPrices.push(seaterPricing.ac, seaterPricing.nonAc);
        }
      } else {
        Object.values(hostel.pricing).forEach((seater: any) => {
          if (filters.acType === 'ac') {
            hostelPrices.push(seater.ac);
          } else if (filters.acType === 'non-ac') {
            hostelPrices.push(seater.nonAc);
          } else {
            hostelPrices.push(seater.ac, seater.nonAc);
          }
        });
      }

      const hostelMinPrice = Math.min(...hostelPrices);
      const hostelMaxPrice = Math.max(...hostelPrices);

      if (hostelMaxPrice < minPrice || hostelMinPrice > maxPrice) {
        return false;
      }
    }

    // Amenities filter
    if (filters.amenities && filters.amenities.length > 0) {
      const hasAllAmenities = filters.amenities.every(amenity => {
        return hostel.amenities[amenity] === true;
      });
      if (!hasAllAmenities) return false;
    }

    // Distance filter
    if (filters.maxDistance && filters.userLocation) {
      const distance = calculateDistance(
        filters.userLocation.lat,
        filters.userLocation.lng,
        hostel.address.coordinates.lat,
        hostel.address.coordinates.lng
      );
      if (distance > filters.maxDistance) return false;
    }

    return true;
  });
}
