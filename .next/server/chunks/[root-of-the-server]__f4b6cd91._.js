module.exports = {

"[project]/.next-internal/server/app/api/seed/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
let cached = global.mongoose || {
    conn: null,
    promise: null
};
if (!global.mongoose) {
    global.mongoose = cached;
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts);
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/models/User.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
;
;
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true
    },
    password: {
        type: String,
        required: true,
        minlength: 6
    },
    role: {
        type: String,
        enum: [
            'admin',
            'user'
        ],
        default: 'user'
    },
    name: {
        type: String,
        required: true,
        trim: true
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
// Hash password before saving
UserSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    try {
        const salt = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].genSalt(12);
        this.password = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});
// Compare password method
UserSchema.methods.comparePassword = async function(candidatePassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(candidatePassword, this.password);
};
// Create indexes
UserSchema.index({
    email: 1
});
UserSchema.index({
    role: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[project]/src/models/Hostel.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const PhotoSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    url: {
        type: String,
        required: true
    },
    alt: {
        type: String,
        required: true
    },
    category: {
        type: String,
        required: true,
        enum: [
            'main',
            '1-seater-ac',
            '1-seater-non-ac',
            '2-seater-ac',
            '2-seater-non-ac',
            '3-seater-ac',
            '3-seater-non-ac',
            '4-seater-ac',
            '4-seater-non-ac',
            'common-area',
            'kitchen',
            'bathroom',
            'exterior'
        ]
    }
});
const PricingSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    '1-seater': {
        ac: {
            type: Number,
            required: true
        },
        nonAc: {
            type: Number,
            required: true
        }
    },
    '2-seater': {
        ac: {
            type: Number,
            required: true
        },
        nonAc: {
            type: Number,
            required: true
        }
    },
    '3-seater': {
        ac: {
            type: Number,
            required: true
        },
        nonAc: {
            type: Number,
            required: true
        }
    },
    '4-seater': {
        ac: {
            type: Number,
            required: true
        },
        nonAc: {
            type: Number,
            required: true
        }
    }
});
const HostelSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: true
    },
    headline: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    address: {
        street: {
            type: String,
            required: true
        },
        city: {
            type: String,
            required: true
        },
        state: {
            type: String,
            required: true
        },
        pincode: {
            type: String,
            required: true
        },
        coordinates: {
            lat: {
                type: Number,
                required: true
            },
            lng: {
                type: Number,
                required: true
            }
        }
    },
    photos: [
        PhotoSchema
    ],
    pricing: {
        type: PricingSchema,
        required: true
    },
    roomSizes: {
        '1-seater': {
            type: String,
            required: true
        },
        '2-seater': {
            type: String,
            required: true
        },
        '3-seater': {
            type: String,
            required: true
        },
        '4-seater': {
            type: String,
            required: true
        }
    },
    amenities: {
        wifi: {
            type: Boolean,
            default: false
        },
        parking: {
            type: Boolean,
            default: false
        },
        laundry: {
            type: Boolean,
            default: false
        },
        meals: {
            type: Boolean,
            default: false
        },
        ac: {
            type: Boolean,
            default: false
        },
        powerBackup: {
            type: Boolean,
            default: false
        },
        studyRoom: {
            type: Boolean,
            default: false
        },
        commonRoom: {
            type: Boolean,
            default: false
        },
        gym: {
            type: Boolean,
            default: false
        },
        indoorGames: {
            type: Boolean,
            default: false
        },
        outdoorGames: {
            type: Boolean,
            default: false
        },
        library: {
            type: Boolean,
            default: false
        },
        medical: {
            type: Boolean,
            default: false
        },
        security: {
            type: Boolean,
            default: false
        },
        cctv: {
            type: Boolean,
            default: false
        },
        biometric: {
            type: Boolean,
            default: false
        },
        other: [
            {
                type: String
            }
        ]
    },
    nearbyLandmarks: [
        {
            type: String
        }
    ],
    nearestColleges: [
        {
            name: {
                type: String,
                required: true
            },
            distance: {
                type: String,
                required: true
            }
        }
    ],
    foodMenu: [
        {
            category: {
                type: String,
                required: true
            },
            items: [
                {
                    name: {
                        type: String,
                        required: true
                    },
                    price: {
                        type: Number,
                        required: true
                    },
                    description: {
                        type: String
                    }
                }
            ]
        }
    ],
    collegeLunchProvision: {
        type: Boolean,
        default: false
    },
    securityFeatures: {
        cctv: {
            type: Boolean,
            default: false
        },
        securityGuard: {
            type: Boolean,
            default: false
        },
        biometricAccess: {
            type: Boolean,
            default: false
        },
        timeRestrictions: {
            enabled: {
                type: Boolean,
                default: false
            },
            inTime: {
                type: String
            },
            outTime: {
                type: String
            }
        }
    },
    caretaker: {
        name: {
            type: String,
            required: true
        },
        phone: {
            type: String,
            required: true
        },
        availability: {
            type: String,
            required: true
        }
    },
    gymFacilities: [
        {
            type: String
        }
    ],
    indoorSportsFacilities: [
        {
            type: String
        }
    ],
    eventsAndFests: [
        {
            name: {
                type: String,
                required: true
            },
            description: {
                type: String,
                required: true
            },
            frequency: {
                type: String,
                required: true
            }
        }
    ],
    travelFacilities: [
        {
            type: String
        }
    ],
    owner: {
        name: {
            type: String,
            required: true
        },
        phone: {
            type: String,
            required: true
        },
        email: {
            type: String
        }
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
// Create indexes for better query performance
HostelSchema.index({
    'address.coordinates': '2dsphere'
});
HostelSchema.index({
    name: 'text',
    headline: 'text',
    description: 'text'
});
HostelSchema.index({
    isActive: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Hostel || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Hostel', HostelSchema);
}}),
"[project]/src/app/api/seed/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Hostel.ts [app-route] (ecmascript)");
;
;
;
;
async function POST() {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        // Create admin user
        const adminExists = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            email: '<EMAIL>'
        });
        if (!adminExists) {
            const admin = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                email: '<EMAIL>',
                password: 'nk10nikhil',
                name: 'Admin User',
                role: 'admin'
            });
            await admin.save();
            console.log('Admin user created');
        }
        // Create sample hostels
        const hostelCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments();
        if (hostelCount === 0) {
            const sampleHostels = [
                {
                    name: "Green Valley Hostel",
                    headline: "Premium accommodation with modern amenities",
                    description: "A well-maintained hostel offering comfortable living spaces with all modern amenities. Located in a peaceful environment perfect for students.",
                    address: {
                        street: "123 College Road",
                        city: "Bangalore",
                        state: "Karnataka",
                        pincode: "560001",
                        coordinates: {
                            lat: 12.9716,
                            lng: 77.5946
                        }
                    },
                    photos: [
                        {
                            url: "/api/placeholder/800/600",
                            alt: "Green Valley Hostel Main Building",
                            category: "main"
                        },
                        {
                            url: "/api/placeholder/600/400",
                            alt: "Single Seater AC Room",
                            category: "1-seater-ac"
                        },
                        {
                            url: "/api/placeholder/600/400",
                            alt: "Double Seater Non-AC Room",
                            category: "2-seater-non-ac"
                        }
                    ],
                    pricing: {
                        "1-seater": {
                            ac: 15000,
                            nonAc: 12000
                        },
                        "2-seater": {
                            ac: 10000,
                            nonAc: 8000
                        },
                        "3-seater": {
                            ac: 8000,
                            nonAc: 6500
                        },
                        "4-seater": {
                            ac: 7000,
                            nonAc: 5500
                        }
                    },
                    roomSizes: {
                        "1-seater": "12x10 ft",
                        "2-seater": "14x12 ft",
                        "3-seater": "16x14 ft",
                        "4-seater": "18x16 ft"
                    },
                    amenities: {
                        wifi: true,
                        parking: true,
                        laundry: true,
                        meals: true,
                        ac: true,
                        powerBackup: true,
                        studyRoom: true,
                        commonRoom: true,
                        gym: true,
                        indoorGames: true,
                        outdoorGames: false,
                        library: true,
                        medical: true,
                        security: true,
                        cctv: true,
                        biometric: true,
                        other: [
                            "Water Purifier",
                            "Refrigerator"
                        ]
                    },
                    nearbyLandmarks: [
                        "City Mall",
                        "Central Park",
                        "Metro Station"
                    ],
                    nearestColleges: [
                        {
                            name: "ABC Engineering College",
                            distance: "2 km"
                        },
                        {
                            name: "XYZ Medical College",
                            distance: "3.5 km"
                        }
                    ],
                    foodMenu: [
                        {
                            category: "Breakfast",
                            items: [
                                {
                                    name: "Idli Sambar",
                                    price: 40,
                                    description: "South Indian breakfast"
                                },
                                {
                                    name: "Poha",
                                    price: 35,
                                    description: "Maharashtrian breakfast"
                                }
                            ]
                        },
                        {
                            category: "Lunch",
                            items: [
                                {
                                    name: "Veg Thali",
                                    price: 80,
                                    description: "Complete vegetarian meal"
                                },
                                {
                                    name: "Non-Veg Thali",
                                    price: 120,
                                    description: "Complete non-vegetarian meal"
                                }
                            ]
                        }
                    ],
                    collegeLunchProvision: true,
                    securityFeatures: {
                        cctv: true,
                        securityGuard: true,
                        biometricAccess: true,
                        timeRestrictions: {
                            enabled: true,
                            inTime: "10:00 PM",
                            outTime: "6:00 AM"
                        }
                    },
                    caretaker: {
                        name: "Ramesh Kumar",
                        phone: "9876543210",
                        availability: "24/7"
                    },
                    gymFacilities: [
                        "Treadmill",
                        "Dumbbells",
                        "Bench Press"
                    ],
                    indoorSportsFacilities: [
                        "Table Tennis",
                        "Carrom",
                        "Chess"
                    ],
                    eventsAndFests: [
                        {
                            name: "Annual Sports Day",
                            description: "Inter-hostel sports competition",
                            frequency: "Yearly"
                        }
                    ],
                    travelFacilities: [
                        "Bus Stop nearby",
                        "Metro connectivity"
                    ],
                    owner: {
                        name: "Suresh Patel",
                        phone: "9123456789",
                        email: "<EMAIL>"
                    }
                },
                {
                    name: "Sunrise Residency",
                    headline: "Budget-friendly accommodation for students",
                    description: "Affordable hostel with basic amenities and good connectivity to major colleges and universities.",
                    address: {
                        street: "456 University Avenue",
                        city: "Pune",
                        state: "Maharashtra",
                        pincode: "411001",
                        coordinates: {
                            lat: 18.5204,
                            lng: 73.8567
                        }
                    },
                    photos: [
                        {
                            url: "/api/placeholder/800/600",
                            alt: "Sunrise Residency Building",
                            category: "main"
                        }
                    ],
                    pricing: {
                        "1-seater": {
                            ac: 12000,
                            nonAc: 9000
                        },
                        "2-seater": {
                            ac: 8000,
                            nonAc: 6000
                        },
                        "3-seater": {
                            ac: 6500,
                            nonAc: 5000
                        },
                        "4-seater": {
                            ac: 5500,
                            nonAc: 4500
                        }
                    },
                    roomSizes: {
                        "1-seater": "10x10 ft",
                        "2-seater": "12x12 ft",
                        "3-seater": "14x12 ft",
                        "4-seater": "16x14 ft"
                    },
                    amenities: {
                        wifi: true,
                        parking: false,
                        laundry: true,
                        meals: true,
                        ac: false,
                        powerBackup: true,
                        studyRoom: false,
                        commonRoom: true,
                        gym: false,
                        indoorGames: true,
                        outdoorGames: false,
                        library: false,
                        medical: false,
                        security: true,
                        cctv: true,
                        biometric: false,
                        other: [
                            "Water Purifier"
                        ]
                    },
                    nearbyLandmarks: [
                        "Shopping Complex",
                        "Bus Terminal"
                    ],
                    nearestColleges: [
                        {
                            name: "Pune University",
                            distance: "1.5 km"
                        },
                        {
                            name: "Engineering Institute",
                            distance: "2 km"
                        }
                    ],
                    foodMenu: [
                        {
                            category: "Breakfast",
                            items: [
                                {
                                    name: "Bread Butter",
                                    price: 25
                                },
                                {
                                    name: "Tea",
                                    price: 10
                                }
                            ]
                        }
                    ],
                    collegeLunchProvision: false,
                    securityFeatures: {
                        cctv: true,
                        securityGuard: false,
                        biometricAccess: false,
                        timeRestrictions: {
                            enabled: true,
                            inTime: "11:00 PM",
                            outTime: "5:00 AM"
                        }
                    },
                    caretaker: {
                        name: "Prakash Singh",
                        phone: "9876543211",
                        availability: "9 AM - 9 PM"
                    },
                    gymFacilities: [],
                    indoorSportsFacilities: [
                        "Carrom"
                    ],
                    eventsAndFests: [],
                    travelFacilities: [
                        "Bus connectivity"
                    ],
                    owner: {
                        name: "Rajesh Sharma",
                        phone: "9123456788"
                    }
                }
            ];
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].insertMany(sampleHostels);
            console.log('Sample hostels created');
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'Database seeded successfully'
        });
    } catch (error) {
        console.error('Seed error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f4b6cd91._.js.map