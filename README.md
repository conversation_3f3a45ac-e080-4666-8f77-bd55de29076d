# MyHostel - Comprehensive Hostel Listing Platform

A full-stack web application built with Next.js, MongoDB, and TypeScript for managing and discovering hostel accommodations. This platform provides a complete solution for students to find, compare, and explore hostel options with detailed information and interactive features.

## 🚀 Features

### 🏠 Main Hostels Listing Page
- **Grid and List View Options**: Toggle between different viewing modes
- **Smart Pricing Display**: Shows specific seater pricing when filtered, or price range when no filter applied
- **Advanced Filtering System**:
  - Seater type (1, 2, 3, 4 seater)
  - AC/Non-AC options
  - Price range slider
  - Distance from user location
  - Amenities filter
  - Custom sorting options

### 🏢 Individual Hostel Detail Page
- **Comprehensive Information Display**:
  - Basic details (name, headline, address, location on map)
  - Nearby landmarks and colleges with distances
  - Detailed pricing structure for all room types
  - Interactive photo gallery with category filters
  - Complete amenities list
  - Food menu in table format
  - Security features and restrictions
  - Caretaker and owner contact information
  - Gym and sports facilities
  - Events and travel information
  - Real-time distance calculation from user location

### 🔐 Admin Dashboard
- **Secure Authentication**: Email/password login system
- **Hostel Management**:
  - Add new hostels with comprehensive forms
  - Edit existing hostel details
  - Delete hostels
  - View all hostels in organized table format
- **Dashboard Statistics**: Overview of total hostels, active listings, and cities covered

### 🔍 Hostel Comparison Page
- **Side-by-side Comparison**: Compare up to 3 hostels simultaneously
- **Detailed Feature Matrix**: Compare pricing, amenities, facilities, and contact information
- **Interactive Selection**: Easy hostel selection and removal from comparison

### 📱 Technical Features
- **Fully Responsive Design**: Optimized for all device sizes
- **Real-time Geolocation**: Distance calculations and location-based sorting
- **Interactive Maps**: Location visualization (ready for Google Maps integration)
- **Advanced Search**: Text-based search across multiple fields
- **Production-ready Code**: Optimized performance and error handling

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, Heroicons
- **Backend**: Next.js API Routes
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT-based authentication with HTTP-only cookies
- **State Management**: React Hooks
- **Notifications**: React Hot Toast
- **Maps**: Leaflet (with React Leaflet)
- **Deployment Ready**: Vercel-optimized

## 📋 Prerequisites

- Node.js 18+
- MongoDB Atlas account or local MongoDB installation
- Git

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd myhostel
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Setup
Create a `.env.local` file in the root directory:

```env
# MongoDB Connection
MONGODB_URI=mongodb+srv://your-username:<EMAIL>/hostel-platform?retryWrites=true&w=majority

# Authentication
NEXTAUTH_URL=http://localhost:3001
NEXTAUTH_SECRET=your-super-secret-key-here
JWT_SECRET=your-jwt-secret-key-here

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=nk10nikhil

# Optional: Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
```

### 4. Database Setup
Seed the database with initial data:
```bash
# Start the development server first
npm run dev

# In another terminal, seed the database
curl -X POST http://localhost:3001/api/seed
```

### 5. Access the Application
- **Main Website**: http://localhost:3001
- **Admin Login**: http://localhost:3001/admin/login
  - Email: <EMAIL>
  - Password: nk10nikhil

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin dashboard pages
│   ├── api/               # API routes
│   ├── compare/           # Hostel comparison page
│   ├── hostel/[id]/       # Individual hostel pages
│   ├── hostels/           # Hostels listing page
│   └── page.tsx           # Homepage
├── components/            # Reusable React components
├── lib/                   # Utility functions and database
├── models/                # MongoDB/Mongoose models
└── styles/                # Global styles
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - Admin login
- `POST /api/auth/logout` - Logout
- `GET /api/auth/me` - Get current user

### Hostels
- `GET /api/hostels` - Get all hostels with filtering/pagination
- `POST /api/hostels` - Create new hostel (admin only)
- `GET /api/hostels/[id]` - Get specific hostel
- `PUT /api/hostels/[id]` - Update hostel (admin only)
- `DELETE /api/hostels/[id]` - Delete hostel (admin only)

### Database
- `POST /api/seed` - Seed database with sample data

## 🎨 Key Components

### Header Component
- Responsive navigation
- User authentication status
- Mobile-friendly menu

### Hostel Cards
- Grid and list view support
- Dynamic pricing display
- Interactive elements

### Filter System
- Multi-criteria filtering
- Real-time updates
- Persistent state management

### Photo Gallery
- Category-based filtering
- Modal view with navigation
- Responsive image handling

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **HTTP-only Cookies**: Prevents XSS attacks
- **Input Validation**: Server-side validation for all inputs
- **Role-based Access**: Admin-only routes protection
- **Environment Variables**: Sensitive data protection

## 🌐 Deployment

### Vercel Deployment (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Manual Deployment
```bash
npm run build
npm start
```

## 📊 Database Schema

### Hostel Model
- Basic information (name, headline, description)
- Address with coordinates
- Pricing for all room types
- Comprehensive amenities
- Security features
- Contact information
- Photos with categories
- Food menu
- Facilities and events

### User Model
- Authentication credentials
- Role-based permissions
- Profile information

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Create an issue in the repository

## 🔮 Future Enhancements

- [ ] Real-time chat system
- [ ] Booking system integration
- [ ] Payment gateway integration
- [ ] Review and rating system
- [ ] Mobile app development
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Social media integration

---

Built with ❤️ using Next.js and modern web technologies.
