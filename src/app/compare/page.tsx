'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import { 
  BuildingOfficeIcon,
  PlusIcon,
  XMarkIcon,
  MapPinIcon,
  CurrencyRupeeIcon
} from '@heroicons/react/24/outline';
import { formatPriceRange } from '@/lib/utils';
import toast from 'react-hot-toast';

interface Hostel {
  _id: string;
  name: string;
  headline: string;
  address: {
    city: string;
    state: string;
  };
  photos: Array<{
    url: string;
    alt: string;
    category: string;
  }>;
  pricing: any;
  amenities: any;
  roomSizes: any;
  securityFeatures: any;
  caretaker: any;
  owner: any;
}

export default function ComparePage() {
  const [allHostels, setAllHostels] = useState<Hostel[]>([]);
  const [selectedHostels, setSelectedHostels] = useState<Hostel[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchHostels();
  }, []);

  const fetchHostels = async () => {
    try {
      const response = await fetch('/api/hostels?limit=100');
      if (response.ok) {
        const data = await response.json();
        setAllHostels(data.hostels);
      }
    } catch (error) {
      toast.error('Failed to fetch hostels');
    } finally {
      setIsLoading(false);
    }
  };

  const addHostelToComparison = (hostel: Hostel) => {
    if (selectedHostels.length >= 3) {
      toast.error('You can compare maximum 3 hostels at a time');
      return;
    }
    
    if (selectedHostels.find(h => h._id === hostel._id)) {
      toast.error('Hostel already added to comparison');
      return;
    }
    
    setSelectedHostels([...selectedHostels, hostel]);
    toast.success('Hostel added to comparison');
  };

  const removeHostelFromComparison = (hostelId: string) => {
    setSelectedHostels(selectedHostels.filter(h => h._id !== hostelId));
  };

  const filteredHostels = allHostels.filter(hostel =>
    hostel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    hostel.address.city.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const comparisonFeatures = [
    { key: 'name', label: 'Name' },
    { key: 'headline', label: 'Headline' },
    { key: 'location', label: 'Location' },
    { key: 'pricing', label: 'Price Range' },
    { key: 'roomSizes', label: 'Room Sizes' },
    { key: 'wifi', label: 'WiFi' },
    { key: 'parking', label: 'Parking' },
    { key: 'meals', label: 'Meals' },
    { key: 'gym', label: 'Gym' },
    { key: 'security', label: 'Security' },
    { key: 'cctv', label: 'CCTV' },
    { key: 'caretaker', label: 'Caretaker' },
    { key: 'owner', label: 'Owner Contact' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Compare Hostels</h1>
          <p className="text-gray-600">Select up to 3 hostels to compare their features side by side</p>
        </div>

        {/* Selected Hostels for Comparison */}
        {selectedHostels.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Selected for Comparison ({selectedHostels.length}/3)</h2>
              <button
                onClick={() => setSelectedHostels([])}
                className="text-red-600 hover:text-red-800 text-sm font-medium"
              >
                Clear All
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {selectedHostels.map((hostel) => (
                <div key={hostel._id} className="relative border rounded-lg p-4">
                  <button
                    onClick={() => removeHostelFromComparison(hostel._id)}
                    className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                  <div className="h-32 bg-gray-200 rounded mb-3">
                    {hostel.photos.length > 0 ? (
                      <img
                        src={hostel.photos[0].url}
                        alt={hostel.name}
                        className="w-full h-full object-cover rounded"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <BuildingOfficeIcon className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <h3 className="font-semibold text-sm">{hostel.name}</h3>
                  <p className="text-xs text-gray-600">{hostel.address.city}</p>
                </div>
              ))}
              
              {/* Add more slots */}
              {selectedHostels.length < 3 && (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center h-48">
                  <div className="text-center">
                    <PlusIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">Add hostel to compare</p>
                  </div>
                </div>
              )}
            </div>

            {/* Comparison Table */}
            {selectedHostels.length >= 2 && (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr>
                      <th className="text-left py-3 px-4 border-b font-medium">Feature</th>
                      {selectedHostels.map((hostel) => (
                        <th key={hostel._id} className="text-left py-3 px-4 border-b font-medium">
                          {hostel.name}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {comparisonFeatures.map((feature) => (
                      <tr key={feature.key} className="border-b">
                        <td className="py-3 px-4 font-medium text-gray-700">{feature.label}</td>
                        {selectedHostels.map((hostel) => (
                          <td key={hostel._id} className="py-3 px-4">
                            {feature.key === 'name' && hostel.name}
                            {feature.key === 'headline' && hostel.headline}
                            {feature.key === 'location' && `${hostel.address.city}, ${hostel.address.state}`}
                            {feature.key === 'pricing' && formatPriceRange(hostel.pricing)}
                            {feature.key === 'roomSizes' && (
                              <div className="text-sm">
                                {Object.entries(hostel.roomSizes).map(([type, size]) => (
                                  <div key={type}>{type}: {size as string}</div>
                                ))}
                              </div>
                            )}
                            {['wifi', 'parking', 'meals', 'gym', 'security', 'cctv'].includes(feature.key) && (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                hostel.amenities[feature.key] 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {hostel.amenities[feature.key] ? 'Yes' : 'No'}
                              </span>
                            )}
                            {feature.key === 'caretaker' && (
                              <div className="text-sm">
                                <div>{hostel.caretaker.name}</div>
                                <div className="text-gray-600">{hostel.caretaker.phone}</div>
                              </div>
                            )}
                            {feature.key === 'owner' && (
                              <div className="text-sm">
                                <div>{hostel.owner.name}</div>
                                <div className="text-gray-600">{hostel.owner.phone}</div>
                              </div>
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Search and Select Hostels */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Select Hostels to Compare</h2>
          
          {/* Search */}
          <div className="mb-6">
            <input
              type="text"
              placeholder="Search hostels by name or city..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Hostels Grid */}
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="border rounded-lg p-4 animate-pulse">
                  <div className="h-32 bg-gray-300 rounded mb-3"></div>
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredHostels.map((hostel) => {
                const isSelected = selectedHostels.find(h => h._id === hostel._id);
                return (
                  <div
                    key={hostel._id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      isSelected 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => !isSelected && addHostelToComparison(hostel)}
                  >
                    <div className="h-32 bg-gray-200 rounded mb-3">
                      {hostel.photos.length > 0 ? (
                        <img
                          src={hostel.photos[0].url}
                          alt={hostel.name}
                          className="w-full h-full object-cover rounded"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <BuildingOfficeIcon className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <h3 className="font-semibold mb-1">{hostel.name}</h3>
                    <div className="flex items-center text-gray-600 text-sm mb-2">
                      <MapPinIcon className="h-4 w-4 mr-1" />
                      <span>{hostel.address.city}, {hostel.address.state}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-blue-600 font-semibold">
                        {formatPriceRange(hostel.pricing)}
                      </span>
                      {isSelected ? (
                        <span className="text-blue-600 text-sm font-medium">Selected</span>
                      ) : (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            addHostelToComparison(hostel);
                          }}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          Add to Compare
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {!isLoading && filteredHostels.length === 0 && (
            <div className="text-center py-8">
              <BuildingOfficeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No hostels found matching your search</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
