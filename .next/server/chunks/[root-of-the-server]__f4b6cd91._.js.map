{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached: MongooseCache = global.mongoose || { conn: null, promise: null };\n\nif (!global.mongoose) {\n  global.mongoose = cached;\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAWA,IAAI,SAAwB,OAAO,QAAQ,IAAI;IAAE,MAAM;IAAM,SAAS;AAAK;AAE3E,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;AACpB;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa;IACjD;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nexport interface I<PERSON>ser extends Document {\n  email: string;\n  password: string;\n  role: 'admin' | 'user';\n  name: string;\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n  comparePassword(candidatePassword: string): Promise<boolean>;\n}\n\nconst UserSchema = new Schema<IUser>({\n  email: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true\n  },\n  password: {\n    type: String,\n    required: true,\n    minlength: 6\n  },\n  role: {\n    type: String,\n    enum: ['admin', 'user'],\n    default: 'user'\n  },\n  name: {\n    type: String,\n    required: true,\n    trim: true\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true\n});\n\n// Hash password before saving\nUserSchema.pre('save', async function(next) {\n  if (!this.isModified('password')) return next();\n  \n  try {\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error: any) {\n    next(error);\n  }\n});\n\n// Compare password method\nUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Create indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAaA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,OAAO;QACL,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAS;SAAO;QACvB,SAAS;IACX;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAe,IAAI;IACxC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAY;QACnB,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAyB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,iBAAiB;AACjB,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;uCAEZ,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/models/Hostel.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IPhoto {\n  url: string;\n  alt: string;\n  category: 'main' | '1-seater-ac' | '1-seater-non-ac' | '2-seater-ac' | '2-seater-non-ac' | \n           '3-seater-ac' | '3-seater-non-ac' | '4-seater-ac' | '4-seater-non-ac' | \n           'common-area' | 'kitchen' | 'bathroom' | 'exterior';\n}\n\nexport interface IPricing {\n  '1-seater': {\n    ac: number;\n    nonAc: number;\n  };\n  '2-seater': {\n    ac: number;\n    nonAc: number;\n  };\n  '3-seater': {\n    ac: number;\n    nonAc: number;\n  };\n  '4-seater': {\n    ac: number;\n    nonAc: number;\n  };\n}\n\nexport interface IRoomSizes {\n  '1-seater': string;\n  '2-seater': string;\n  '3-seater': string;\n  '4-seater': string;\n}\n\nexport interface IFoodMenu {\n  category: string;\n  items: {\n    name: string;\n    price: number;\n    description?: string;\n  }[];\n}\n\nexport interface ICaretaker {\n  name: string;\n  phone: string;\n  availability: string;\n}\n\nexport interface IHostel extends Document {\n  name: string;\n  headline: string;\n  description: string;\n  address: {\n    street: string;\n    city: string;\n    state: string;\n    pincode: string;\n    coordinates: {\n      lat: number;\n      lng: number;\n    };\n  };\n  photos: IPhoto[];\n  pricing: IPricing;\n  roomSizes: IRoomSizes;\n  \n  // Amenities\n  amenities: {\n    wifi: boolean;\n    parking: boolean;\n    laundry: boolean;\n    meals: boolean;\n    ac: boolean;\n    powerBackup: boolean;\n    studyRoom: boolean;\n    commonRoom: boolean;\n    gym: boolean;\n    indoorGames: boolean;\n    outdoorGames: boolean;\n    library: boolean;\n    medical: boolean;\n    security: boolean;\n    cctv: boolean;\n    biometric: boolean;\n    other: string[];\n  };\n  \n  // Location details\n  nearbyLandmarks: string[];\n  nearestColleges: {\n    name: string;\n    distance: string;\n  }[];\n  \n  // Food and dining\n  foodMenu: IFoodMenu[];\n  collegeLunchProvision: boolean;\n  \n  // Security and rules\n  securityFeatures: {\n    cctv: boolean;\n    securityGuard: boolean;\n    biometricAccess: boolean;\n    timeRestrictions: {\n      enabled: boolean;\n      inTime: string;\n      outTime: string;\n    };\n  };\n  \n  // Staff\n  caretaker: ICaretaker;\n  \n  // Facilities\n  gymFacilities: string[];\n  indoorSportsFacilities: string[];\n  \n  // Events and activities\n  eventsAndFests: {\n    name: string;\n    description: string;\n    frequency: string;\n  }[];\n  \n  // Travel facilities\n  travelFacilities: string[];\n  \n  // Contact information\n  owner: {\n    name: string;\n    phone: string;\n    email?: string;\n  };\n  \n  // Metadata\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst PhotoSchema = new Schema<IPhoto>({\n  url: { type: String, required: true },\n  alt: { type: String, required: true },\n  category: { \n    type: String, \n    required: true,\n    enum: ['main', '1-seater-ac', '1-seater-non-ac', '2-seater-ac', '2-seater-non-ac', \n           '3-seater-ac', '3-seater-non-ac', '4-seater-ac', '4-seater-non-ac', \n           'common-area', 'kitchen', 'bathroom', 'exterior']\n  }\n});\n\nconst PricingSchema = new Schema<IPricing>({\n  '1-seater': {\n    ac: { type: Number, required: true },\n    nonAc: { type: Number, required: true }\n  },\n  '2-seater': {\n    ac: { type: Number, required: true },\n    nonAc: { type: Number, required: true }\n  },\n  '3-seater': {\n    ac: { type: Number, required: true },\n    nonAc: { type: Number, required: true }\n  },\n  '4-seater': {\n    ac: { type: Number, required: true },\n    nonAc: { type: Number, required: true }\n  }\n});\n\nconst HostelSchema = new Schema<IHostel>({\n  name: { type: String, required: true },\n  headline: { type: String, required: true },\n  description: { type: String, required: true },\n  \n  address: {\n    street: { type: String, required: true },\n    city: { type: String, required: true },\n    state: { type: String, required: true },\n    pincode: { type: String, required: true },\n    coordinates: {\n      lat: { type: Number, required: true },\n      lng: { type: Number, required: true }\n    }\n  },\n  \n  photos: [PhotoSchema],\n  pricing: { type: PricingSchema, required: true },\n  \n  roomSizes: {\n    '1-seater': { type: String, required: true },\n    '2-seater': { type: String, required: true },\n    '3-seater': { type: String, required: true },\n    '4-seater': { type: String, required: true }\n  },\n  \n  amenities: {\n    wifi: { type: Boolean, default: false },\n    parking: { type: Boolean, default: false },\n    laundry: { type: Boolean, default: false },\n    meals: { type: Boolean, default: false },\n    ac: { type: Boolean, default: false },\n    powerBackup: { type: Boolean, default: false },\n    studyRoom: { type: Boolean, default: false },\n    commonRoom: { type: Boolean, default: false },\n    gym: { type: Boolean, default: false },\n    indoorGames: { type: Boolean, default: false },\n    outdoorGames: { type: Boolean, default: false },\n    library: { type: Boolean, default: false },\n    medical: { type: Boolean, default: false },\n    security: { type: Boolean, default: false },\n    cctv: { type: Boolean, default: false },\n    biometric: { type: Boolean, default: false },\n    other: [{ type: String }]\n  },\n  \n  nearbyLandmarks: [{ type: String }],\n  nearestColleges: [{\n    name: { type: String, required: true },\n    distance: { type: String, required: true }\n  }],\n  \n  foodMenu: [{\n    category: { type: String, required: true },\n    items: [{\n      name: { type: String, required: true },\n      price: { type: Number, required: true },\n      description: { type: String }\n    }]\n  }],\n  \n  collegeLunchProvision: { type: Boolean, default: false },\n  \n  securityFeatures: {\n    cctv: { type: Boolean, default: false },\n    securityGuard: { type: Boolean, default: false },\n    biometricAccess: { type: Boolean, default: false },\n    timeRestrictions: {\n      enabled: { type: Boolean, default: false },\n      inTime: { type: String },\n      outTime: { type: String }\n    }\n  },\n  \n  caretaker: {\n    name: { type: String, required: true },\n    phone: { type: String, required: true },\n    availability: { type: String, required: true }\n  },\n  \n  gymFacilities: [{ type: String }],\n  indoorSportsFacilities: [{ type: String }],\n  \n  eventsAndFests: [{\n    name: { type: String, required: true },\n    description: { type: String, required: true },\n    frequency: { type: String, required: true }\n  }],\n  \n  travelFacilities: [{ type: String }],\n  \n  owner: {\n    name: { type: String, required: true },\n    phone: { type: String, required: true },\n    email: { type: String }\n  },\n  \n  isActive: { type: Boolean, default: true }\n}, {\n  timestamps: true\n});\n\n// Create indexes for better query performance\nHostelSchema.index({ 'address.coordinates': '2dsphere' });\nHostelSchema.index({ name: 'text', headline: 'text', description: 'text' });\nHostelSchema.index({ isActive: 1 });\n\nexport default mongoose.models.Hostel || mongoose.model<IHostel>('Hostel', HostelSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA+IA,MAAM,cAAc,IAAI,yGAAA,CAAA,SAAM,CAAS;IACrC,KAAK;QAAE,MAAM;QAAQ,UAAU;IAAK;IACpC,KAAK;QAAE,MAAM;QAAQ,UAAU;IAAK;IACpC,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;YAAe;YAAmB;YAAe;YACzD;YAAe;YAAmB;YAAe;YACjD;YAAe;YAAW;YAAY;SAAW;IAC1D;AACF;AAEA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAW;IACzC,YAAY;QACV,IAAI;YAAE,MAAM;YAAQ,UAAU;QAAK;QACnC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;IACA,YAAY;QACV,IAAI;YAAE,MAAM;YAAQ,UAAU;QAAK;QACnC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;IACA,YAAY;QACV,IAAI;YAAE,MAAM;YAAQ,UAAU;QAAK;QACnC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;IACA,YAAY;QACV,IAAI;YAAE,MAAM;YAAQ,UAAU;QAAK;QACnC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;IACxC;AACF;AAEA,MAAM,eAAe,IAAI,yGAAA,CAAA,SAAM,CAAU;IACvC,MAAM;QAAE,MAAM;QAAQ,UAAU;IAAK;IACrC,UAAU;QAAE,MAAM;QAAQ,UAAU;IAAK;IACzC,aAAa;QAAE,MAAM;QAAQ,UAAU;IAAK;IAE5C,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,UAAU;QAAK;QACvC,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;QACtC,SAAS;YAAE,MAAM;YAAQ,UAAU;QAAK;QACxC,aAAa;YACX,KAAK;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACpC,KAAK;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QACtC;IACF;IAEA,QAAQ;QAAC;KAAY;IACrB,SAAS;QAAE,MAAM;QAAe,UAAU;IAAK;IAE/C,WAAW;QACT,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC3C,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC3C,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;QAC3C,YAAY;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC7C;IAEA,WAAW;QACT,MAAM;YAAE,MAAM;YAAS,SAAS;QAAM;QACtC,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,OAAO;YAAE,MAAM;YAAS,SAAS;QAAM;QACvC,IAAI;YAAE,MAAM;YAAS,SAAS;QAAM;QACpC,aAAa;YAAE,MAAM;YAAS,SAAS;QAAM;QAC7C,WAAW;YAAE,MAAM;YAAS,SAAS;QAAM;QAC3C,YAAY;YAAE,MAAM;YAAS,SAAS;QAAM;QAC5C,KAAK;YAAE,MAAM;YAAS,SAAS;QAAM;QACrC,aAAa;YAAE,MAAM;YAAS,SAAS;QAAM;QAC7C,cAAc;YAAE,MAAM;YAAS,SAAS;QAAM;QAC9C,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,UAAU;YAAE,MAAM;YAAS,SAAS;QAAM;QAC1C,MAAM;YAAE,MAAM;YAAS,SAAS;QAAM;QACtC,WAAW;YAAE,MAAM;YAAS,SAAS;QAAM;QAC3C,OAAO;YAAC;gBAAE,MAAM;YAAO;SAAE;IAC3B;IAEA,iBAAiB;QAAC;YAAE,MAAM;QAAO;KAAE;IACnC,iBAAiB;QAAC;YAChB,MAAM;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACrC,UAAU;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QAC3C;KAAE;IAEF,UAAU;QAAC;YACT,UAAU;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACzC,OAAO;gBAAC;oBACN,MAAM;wBAAE,MAAM;wBAAQ,UAAU;oBAAK;oBACrC,OAAO;wBAAE,MAAM;wBAAQ,UAAU;oBAAK;oBACtC,aAAa;wBAAE,MAAM;oBAAO;gBAC9B;aAAE;QACJ;KAAE;IAEF,uBAAuB;QAAE,MAAM;QAAS,SAAS;IAAM;IAEvD,kBAAkB;QAChB,MAAM;YAAE,MAAM;YAAS,SAAS;QAAM;QACtC,eAAe;YAAE,MAAM;YAAS,SAAS;QAAM;QAC/C,iBAAiB;YAAE,MAAM;YAAS,SAAS;QAAM;QACjD,kBAAkB;YAChB,SAAS;gBAAE,MAAM;gBAAS,SAAS;YAAM;YACzC,QAAQ;gBAAE,MAAM;YAAO;YACvB,SAAS;gBAAE,MAAM;YAAO;QAC1B;IACF;IAEA,WAAW;QACT,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;QACtC,cAAc;YAAE,MAAM;YAAQ,UAAU;QAAK;IAC/C;IAEA,eAAe;QAAC;YAAE,MAAM;QAAO;KAAE;IACjC,wBAAwB;QAAC;YAAE,MAAM;QAAO;KAAE;IAE1C,gBAAgB;QAAC;YACf,MAAM;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YACrC,aAAa;gBAAE,MAAM;gBAAQ,UAAU;YAAK;YAC5C,WAAW;gBAAE,MAAM;gBAAQ,UAAU;YAAK;QAC5C;KAAE;IAEF,kBAAkB;QAAC;YAAE,MAAM;QAAO;KAAE;IAEpC,OAAO;QACL,MAAM;YAAE,MAAM;YAAQ,UAAU;QAAK;QACrC,OAAO;YAAE,MAAM;YAAQ,UAAU;QAAK;QACtC,OAAO;YAAE,MAAM;QAAO;IACxB;IAEA,UAAU;QAAE,MAAM;QAAS,SAAS;IAAK;AAC3C,GAAG;IACD,YAAY;AACd;AAEA,8CAA8C;AAC9C,aAAa,KAAK,CAAC;IAAE,uBAAuB;AAAW;AACvD,aAAa,KAAK,CAAC;IAAE,MAAM;IAAQ,UAAU;IAAQ,aAAa;AAAO;AACzE,aAAa,KAAK,CAAC;IAAE,UAAU;AAAE;uCAElB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/myhostel/src/app/api/seed/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport User from '@/models/User';\nimport Hostel from '@/models/Hostel';\n\nexport async function POST() {\n  try {\n    await connectDB();\n    \n    // Create admin user\n    const adminExists = await User.findOne({ email: '<EMAIL>' });\n    \n    if (!adminExists) {\n      const admin = new User({\n        email: '<EMAIL>',\n        password: 'nk10nikhil',\n        name: 'Admin User',\n        role: 'admin'\n      });\n      \n      await admin.save();\n      console.log('Admin user created');\n    }\n    \n    // Create sample hostels\n    const hostelCount = await Hostel.countDocuments();\n    \n    if (hostelCount === 0) {\n      const sampleHostels = [\n        {\n          name: \"Green Valley Hostel\",\n          headline: \"Premium accommodation with modern amenities\",\n          description: \"A well-maintained hostel offering comfortable living spaces with all modern amenities. Located in a peaceful environment perfect for students.\",\n          address: {\n            street: \"123 College Road\",\n            city: \"Bangalore\",\n            state: \"Karnataka\",\n            pincode: \"560001\",\n            coordinates: {\n              lat: 12.9716,\n              lng: 77.5946\n            }\n          },\n          photos: [\n            {\n              url: \"/api/placeholder/800/600\",\n              alt: \"Green Valley Hostel Main Building\",\n              category: \"main\"\n            },\n            {\n              url: \"/api/placeholder/600/400\",\n              alt: \"Single Seater AC Room\",\n              category: \"1-seater-ac\"\n            },\n            {\n              url: \"/api/placeholder/600/400\",\n              alt: \"Double Seater Non-AC Room\",\n              category: \"2-seater-non-ac\"\n            }\n          ],\n          pricing: {\n            \"1-seater\": { ac: 15000, nonAc: 12000 },\n            \"2-seater\": { ac: 10000, nonAc: 8000 },\n            \"3-seater\": { ac: 8000, nonAc: 6500 },\n            \"4-seater\": { ac: 7000, nonAc: 5500 }\n          },\n          roomSizes: {\n            \"1-seater\": \"12x10 ft\",\n            \"2-seater\": \"14x12 ft\",\n            \"3-seater\": \"16x14 ft\",\n            \"4-seater\": \"18x16 ft\"\n          },\n          amenities: {\n            wifi: true,\n            parking: true,\n            laundry: true,\n            meals: true,\n            ac: true,\n            powerBackup: true,\n            studyRoom: true,\n            commonRoom: true,\n            gym: true,\n            indoorGames: true,\n            outdoorGames: false,\n            library: true,\n            medical: true,\n            security: true,\n            cctv: true,\n            biometric: true,\n            other: [\"Water Purifier\", \"Refrigerator\"]\n          },\n          nearbyLandmarks: [\"City Mall\", \"Central Park\", \"Metro Station\"],\n          nearestColleges: [\n            { name: \"ABC Engineering College\", distance: \"2 km\" },\n            { name: \"XYZ Medical College\", distance: \"3.5 km\" }\n          ],\n          foodMenu: [\n            {\n              category: \"Breakfast\",\n              items: [\n                { name: \"Idli Sambar\", price: 40, description: \"South Indian breakfast\" },\n                { name: \"Poha\", price: 35, description: \"Maharashtrian breakfast\" }\n              ]\n            },\n            {\n              category: \"Lunch\",\n              items: [\n                { name: \"Veg Thali\", price: 80, description: \"Complete vegetarian meal\" },\n                { name: \"Non-Veg Thali\", price: 120, description: \"Complete non-vegetarian meal\" }\n              ]\n            }\n          ],\n          collegeLunchProvision: true,\n          securityFeatures: {\n            cctv: true,\n            securityGuard: true,\n            biometricAccess: true,\n            timeRestrictions: {\n              enabled: true,\n              inTime: \"10:00 PM\",\n              outTime: \"6:00 AM\"\n            }\n          },\n          caretaker: {\n            name: \"Ramesh Kumar\",\n            phone: \"9876543210\",\n            availability: \"24/7\"\n          },\n          gymFacilities: [\"Treadmill\", \"Dumbbells\", \"Bench Press\"],\n          indoorSportsFacilities: [\"Table Tennis\", \"Carrom\", \"Chess\"],\n          eventsAndFests: [\n            {\n              name: \"Annual Sports Day\",\n              description: \"Inter-hostel sports competition\",\n              frequency: \"Yearly\"\n            }\n          ],\n          travelFacilities: [\"Bus Stop nearby\", \"Metro connectivity\"],\n          owner: {\n            name: \"Suresh Patel\",\n            phone: \"9123456789\",\n            email: \"<EMAIL>\"\n          }\n        },\n        {\n          name: \"Sunrise Residency\",\n          headline: \"Budget-friendly accommodation for students\",\n          description: \"Affordable hostel with basic amenities and good connectivity to major colleges and universities.\",\n          address: {\n            street: \"456 University Avenue\",\n            city: \"Pune\",\n            state: \"Maharashtra\",\n            pincode: \"411001\",\n            coordinates: {\n              lat: 18.5204,\n              lng: 73.8567\n            }\n          },\n          photos: [\n            {\n              url: \"/api/placeholder/800/600\",\n              alt: \"Sunrise Residency Building\",\n              category: \"main\"\n            }\n          ],\n          pricing: {\n            \"1-seater\": { ac: 12000, nonAc: 9000 },\n            \"2-seater\": { ac: 8000, nonAc: 6000 },\n            \"3-seater\": { ac: 6500, nonAc: 5000 },\n            \"4-seater\": { ac: 5500, nonAc: 4500 }\n          },\n          roomSizes: {\n            \"1-seater\": \"10x10 ft\",\n            \"2-seater\": \"12x12 ft\",\n            \"3-seater\": \"14x12 ft\",\n            \"4-seater\": \"16x14 ft\"\n          },\n          amenities: {\n            wifi: true,\n            parking: false,\n            laundry: true,\n            meals: true,\n            ac: false,\n            powerBackup: true,\n            studyRoom: false,\n            commonRoom: true,\n            gym: false,\n            indoorGames: true,\n            outdoorGames: false,\n            library: false,\n            medical: false,\n            security: true,\n            cctv: true,\n            biometric: false,\n            other: [\"Water Purifier\"]\n          },\n          nearbyLandmarks: [\"Shopping Complex\", \"Bus Terminal\"],\n          nearestColleges: [\n            { name: \"Pune University\", distance: \"1.5 km\" },\n            { name: \"Engineering Institute\", distance: \"2 km\" }\n          ],\n          foodMenu: [\n            {\n              category: \"Breakfast\",\n              items: [\n                { name: \"Bread Butter\", price: 25 },\n                { name: \"Tea\", price: 10 }\n              ]\n            }\n          ],\n          collegeLunchProvision: false,\n          securityFeatures: {\n            cctv: true,\n            securityGuard: false,\n            biometricAccess: false,\n            timeRestrictions: {\n              enabled: true,\n              inTime: \"11:00 PM\",\n              outTime: \"5:00 AM\"\n            }\n          },\n          caretaker: {\n            name: \"Prakash Singh\",\n            phone: \"9876543211\",\n            availability: \"9 AM - 9 PM\"\n          },\n          gymFacilities: [],\n          indoorSportsFacilities: [\"Carrom\"],\n          eventsAndFests: [],\n          travelFacilities: [\"Bus connectivity\"],\n          owner: {\n            name: \"Rajesh Sharma\",\n            phone: \"9123456788\"\n          }\n        }\n      ];\n      \n      await Hostel.insertMany(sampleHostels);\n      console.log('Sample hostels created');\n    }\n    \n    return NextResponse.json({\n      message: 'Database seeded successfully'\n    });\n    \n  } catch (error) {\n    console.error('Seed error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,oBAAoB;QACpB,MAAM,cAAc,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAAuB;QAEvE,IAAI,CAAC,aAAa;YAChB,MAAM,QAAQ,IAAI,uHAAA,CAAA,UAAI,CAAC;gBACrB,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,MAAM;YACR;YAEA,MAAM,MAAM,IAAI;YAChB,QAAQ,GAAG,CAAC;QACd;QAEA,wBAAwB;QACxB,MAAM,cAAc,MAAM,yHAAA,CAAA,UAAM,CAAC,cAAc;QAE/C,IAAI,gBAAgB,GAAG;YACrB,MAAM,gBAAgB;gBACpB;oBACE,MAAM;oBACN,UAAU;oBACV,aAAa;oBACb,SAAS;wBACP,QAAQ;wBACR,MAAM;wBACN,OAAO;wBACP,SAAS;wBACT,aAAa;4BACX,KAAK;4BACL,KAAK;wBACP;oBACF;oBACA,QAAQ;wBACN;4BACE,KAAK;4BACL,KAAK;4BACL,UAAU;wBACZ;wBACA;4BACE,KAAK;4BACL,KAAK;4BACL,UAAU;wBACZ;wBACA;4BACE,KAAK;4BACL,KAAK;4BACL,UAAU;wBACZ;qBACD;oBACD,SAAS;wBACP,YAAY;4BAAE,IAAI;4BAAO,OAAO;wBAAM;wBACtC,YAAY;4BAAE,IAAI;4BAAO,OAAO;wBAAK;wBACrC,YAAY;4BAAE,IAAI;4BAAM,OAAO;wBAAK;wBACpC,YAAY;4BAAE,IAAI;4BAAM,OAAO;wBAAK;oBACtC;oBACA,WAAW;wBACT,YAAY;wBACZ,YAAY;wBACZ,YAAY;wBACZ,YAAY;oBACd;oBACA,WAAW;wBACT,MAAM;wBACN,SAAS;wBACT,SAAS;wBACT,OAAO;wBACP,IAAI;wBACJ,aAAa;wBACb,WAAW;wBACX,YAAY;wBACZ,KAAK;wBACL,aAAa;wBACb,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,UAAU;wBACV,MAAM;wBACN,WAAW;wBACX,OAAO;4BAAC;4BAAkB;yBAAe;oBAC3C;oBACA,iBAAiB;wBAAC;wBAAa;wBAAgB;qBAAgB;oBAC/D,iBAAiB;wBACf;4BAAE,MAAM;4BAA2B,UAAU;wBAAO;wBACpD;4BAAE,MAAM;4BAAuB,UAAU;wBAAS;qBACnD;oBACD,UAAU;wBACR;4BACE,UAAU;4BACV,OAAO;gCACL;oCAAE,MAAM;oCAAe,OAAO;oCAAI,aAAa;gCAAyB;gCACxE;oCAAE,MAAM;oCAAQ,OAAO;oCAAI,aAAa;gCAA0B;6BACnE;wBACH;wBACA;4BACE,UAAU;4BACV,OAAO;gCACL;oCAAE,MAAM;oCAAa,OAAO;oCAAI,aAAa;gCAA2B;gCACxE;oCAAE,MAAM;oCAAiB,OAAO;oCAAK,aAAa;gCAA+B;6BAClF;wBACH;qBACD;oBACD,uBAAuB;oBACvB,kBAAkB;wBAChB,MAAM;wBACN,eAAe;wBACf,iBAAiB;wBACjB,kBAAkB;4BAChB,SAAS;4BACT,QAAQ;4BACR,SAAS;wBACX;oBACF;oBACA,WAAW;wBACT,MAAM;wBACN,OAAO;wBACP,cAAc;oBAChB;oBACA,eAAe;wBAAC;wBAAa;wBAAa;qBAAc;oBACxD,wBAAwB;wBAAC;wBAAgB;wBAAU;qBAAQ;oBAC3D,gBAAgB;wBACd;4BACE,MAAM;4BACN,aAAa;4BACb,WAAW;wBACb;qBACD;oBACD,kBAAkB;wBAAC;wBAAmB;qBAAqB;oBAC3D,OAAO;wBACL,MAAM;wBACN,OAAO;wBACP,OAAO;oBACT;gBACF;gBACA;oBACE,MAAM;oBACN,UAAU;oBACV,aAAa;oBACb,SAAS;wBACP,QAAQ;wBACR,MAAM;wBACN,OAAO;wBACP,SAAS;wBACT,aAAa;4BACX,KAAK;4BACL,KAAK;wBACP;oBACF;oBACA,QAAQ;wBACN;4BACE,KAAK;4BACL,KAAK;4BACL,UAAU;wBACZ;qBACD;oBACD,SAAS;wBACP,YAAY;4BAAE,IAAI;4BAAO,OAAO;wBAAK;wBACrC,YAAY;4BAAE,IAAI;4BAAM,OAAO;wBAAK;wBACpC,YAAY;4BAAE,IAAI;4BAAM,OAAO;wBAAK;wBACpC,YAAY;4BAAE,IAAI;4BAAM,OAAO;wBAAK;oBACtC;oBACA,WAAW;wBACT,YAAY;wBACZ,YAAY;wBACZ,YAAY;wBACZ,YAAY;oBACd;oBACA,WAAW;wBACT,MAAM;wBACN,SAAS;wBACT,SAAS;wBACT,OAAO;wBACP,IAAI;wBACJ,aAAa;wBACb,WAAW;wBACX,YAAY;wBACZ,KAAK;wBACL,aAAa;wBACb,cAAc;wBACd,SAAS;wBACT,SAAS;wBACT,UAAU;wBACV,MAAM;wBACN,WAAW;wBACX,OAAO;4BAAC;yBAAiB;oBAC3B;oBACA,iBAAiB;wBAAC;wBAAoB;qBAAe;oBACrD,iBAAiB;wBACf;4BAAE,MAAM;4BAAmB,UAAU;wBAAS;wBAC9C;4BAAE,MAAM;4BAAyB,UAAU;wBAAO;qBACnD;oBACD,UAAU;wBACR;4BACE,UAAU;4BACV,OAAO;gCACL;oCAAE,MAAM;oCAAgB,OAAO;gCAAG;gCAClC;oCAAE,MAAM;oCAAO,OAAO;gCAAG;6BAC1B;wBACH;qBACD;oBACD,uBAAuB;oBACvB,kBAAkB;wBAChB,MAAM;wBACN,eAAe;wBACf,iBAAiB;wBACjB,kBAAkB;4BAChB,SAAS;4BACT,QAAQ;4BACR,SAAS;wBACX;oBACF;oBACA,WAAW;wBACT,MAAM;wBACN,OAAO;wBACP,cAAc;oBAChB;oBACA,eAAe,EAAE;oBACjB,wBAAwB;wBAAC;qBAAS;oBAClC,gBAAgB,EAAE;oBAClB,kBAAkB;wBAAC;qBAAmB;oBACtC,OAAO;wBACL,MAAM;wBACN,OAAO;oBACT;gBACF;aACD;YAED,MAAM,yHAAA,CAAA,UAAM,CAAC,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}