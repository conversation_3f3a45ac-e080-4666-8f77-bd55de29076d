{"name": "myhostel", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.18", "bcryptjs": "^3.0.2", "framer-motion": "^12.16.0", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.513.0", "mongodb": "^6.17.0", "mongoose": "^8.15.1", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-leaflet": "^5.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}