module.exports = {

"[project]/.next-internal/server/app/api/hostels/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
let cached = global.mongoose || {
    conn: null,
    promise: null
};
if (!global.mongoose) {
    global.mongoose = cached;
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts);
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Hostel.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const PhotoSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    url: {
        type: String,
        required: true
    },
    alt: {
        type: String,
        required: true
    },
    category: {
        type: String,
        required: true,
        enum: [
            'main',
            '1-seater-ac',
            '1-seater-non-ac',
            '2-seater-ac',
            '2-seater-non-ac',
            '3-seater-ac',
            '3-seater-non-ac',
            '4-seater-ac',
            '4-seater-non-ac',
            'common-area',
            'kitchen',
            'bathroom',
            'exterior'
        ]
    }
});
const PricingSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    '1-seater': {
        ac: {
            type: Number,
            required: true
        },
        nonAc: {
            type: Number,
            required: true
        }
    },
    '2-seater': {
        ac: {
            type: Number,
            required: true
        },
        nonAc: {
            type: Number,
            required: true
        }
    },
    '3-seater': {
        ac: {
            type: Number,
            required: true
        },
        nonAc: {
            type: Number,
            required: true
        }
    },
    '4-seater': {
        ac: {
            type: Number,
            required: true
        },
        nonAc: {
            type: Number,
            required: true
        }
    }
});
const HostelSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: true
    },
    headline: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    address: {
        street: {
            type: String,
            required: true
        },
        city: {
            type: String,
            required: true
        },
        state: {
            type: String,
            required: true
        },
        pincode: {
            type: String,
            required: true
        },
        coordinates: {
            lat: {
                type: Number,
                required: true
            },
            lng: {
                type: Number,
                required: true
            }
        }
    },
    photos: [
        PhotoSchema
    ],
    pricing: {
        type: PricingSchema,
        required: true
    },
    roomSizes: {
        '1-seater': {
            type: String,
            required: true
        },
        '2-seater': {
            type: String,
            required: true
        },
        '3-seater': {
            type: String,
            required: true
        },
        '4-seater': {
            type: String,
            required: true
        }
    },
    amenities: {
        wifi: {
            type: Boolean,
            default: false
        },
        parking: {
            type: Boolean,
            default: false
        },
        laundry: {
            type: Boolean,
            default: false
        },
        meals: {
            type: Boolean,
            default: false
        },
        ac: {
            type: Boolean,
            default: false
        },
        powerBackup: {
            type: Boolean,
            default: false
        },
        studyRoom: {
            type: Boolean,
            default: false
        },
        commonRoom: {
            type: Boolean,
            default: false
        },
        gym: {
            type: Boolean,
            default: false
        },
        indoorGames: {
            type: Boolean,
            default: false
        },
        outdoorGames: {
            type: Boolean,
            default: false
        },
        library: {
            type: Boolean,
            default: false
        },
        medical: {
            type: Boolean,
            default: false
        },
        security: {
            type: Boolean,
            default: false
        },
        cctv: {
            type: Boolean,
            default: false
        },
        biometric: {
            type: Boolean,
            default: false
        },
        other: [
            {
                type: String
            }
        ]
    },
    nearbyLandmarks: [
        {
            type: String
        }
    ],
    nearestColleges: [
        {
            name: {
                type: String,
                required: true
            },
            distance: {
                type: String,
                required: true
            }
        }
    ],
    foodMenu: [
        {
            category: {
                type: String,
                required: true
            },
            items: [
                {
                    name: {
                        type: String,
                        required: true
                    },
                    price: {
                        type: Number,
                        required: true
                    },
                    description: {
                        type: String
                    }
                }
            ]
        }
    ],
    collegeLunchProvision: {
        type: Boolean,
        default: false
    },
    securityFeatures: {
        cctv: {
            type: Boolean,
            default: false
        },
        securityGuard: {
            type: Boolean,
            default: false
        },
        biometricAccess: {
            type: Boolean,
            default: false
        },
        timeRestrictions: {
            enabled: {
                type: Boolean,
                default: false
            },
            inTime: {
                type: String
            },
            outTime: {
                type: String
            }
        }
    },
    caretaker: {
        name: {
            type: String,
            required: true
        },
        phone: {
            type: String,
            required: true
        },
        availability: {
            type: String,
            required: true
        }
    },
    gymFacilities: [
        {
            type: String
        }
    ],
    indoorSportsFacilities: [
        {
            type: String
        }
    ],
    eventsAndFests: [
        {
            name: {
                type: String,
                required: true
            },
            description: {
                type: String,
                required: true
            },
            frequency: {
                type: String,
                required: true
            }
        }
    ],
    travelFacilities: [
        {
            type: String
        }
    ],
    owner: {
        name: {
            type: String,
            required: true
        },
        phone: {
            type: String,
            required: true
        },
        email: {
            type: String
        }
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
// Create indexes for better query performance
HostelSchema.index({
    'address.coordinates': '2dsphere'
});
HostelSchema.index({
    name: 'text',
    headline: 'text',
    description: 'text'
});
HostelSchema.index({
    isActive: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Hostel || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Hostel', HostelSchema);
}}),
"[project]/src/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateDistance": (()=>calculateDistance),
    "cn": (()=>cn),
    "debounce": (()=>debounce),
    "filterHostels": (()=>filterHostels),
    "formatCurrency": (()=>formatCurrency),
    "formatDistance": (()=>formatDistance),
    "formatPriceRange": (()=>formatPriceRange),
    "generateSlug": (()=>generateSlug),
    "getCurrentLocation": (()=>getCurrentLocation),
    "isValidEmail": (()=>isValidEmail),
    "isValidPhone": (()=>isValidPhone),
    "sortHostelsByDistance": (()=>sortHostelsByDistance),
    "truncateText": (()=>truncateText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c; // Distance in kilometers
    return Math.round(d * 100) / 100; // Round to 2 decimal places
}
function deg2rad(deg) {
    return deg * (Math.PI / 180);
}
function formatPriceRange(pricing, seaterType) {
    if (seaterType) {
        const seaterPricing = pricing[seaterType];
        if (seaterPricing) {
            const min = Math.min(seaterPricing.ac, seaterPricing.nonAc);
            const max = Math.max(seaterPricing.ac, seaterPricing.nonAc);
            return min === max ? `₹${min}` : `₹${min} - ₹${max}`;
        }
    }
    // Get all prices and find min/max
    const allPrices = [];
    Object.values(pricing).forEach((seater)=>{
        allPrices.push(seater.ac, seater.nonAc);
    });
    const min = Math.min(...allPrices);
    const max = Math.max(...allPrices);
    return min === max ? `₹${min}` : `₹${min} - ₹${max}`;
}
function getCurrentLocation() {
    return new Promise((resolve, reject)=>{
        if (!navigator.geolocation) {
            reject(new Error('Geolocation is not supported by this browser.'));
            return;
        }
        navigator.geolocation.getCurrentPosition((position)=>resolve(position), (error)=>reject(error), {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5 minutes
        });
    });
}
function formatDistance(distance) {
    if (distance < 1) {
        return `${Math.round(distance * 1000)}m`;
    }
    return `${distance}km`;
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidPhone(phone) {
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
}
function generateSlug(text) {
    return text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').trim();
}
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
}
function sortHostelsByDistance(hostels, userLat, userLng) {
    if (!userLat || !userLng) return hostels;
    return hostels.map((hostel)=>({
            ...hostel,
            distance: calculateDistance(userLat, userLng, hostel.address.coordinates.lat, hostel.address.coordinates.lng)
        })).sort((a, b)=>a.distance - b.distance);
}
function filterHostels(hostels, filters) {
    return hostels.filter((hostel)=>{
        // Seater type filter
        if (filters.seaterType && filters.seaterType !== 'all') {
            const seaterPricing = hostel.pricing[filters.seaterType];
            if (!seaterPricing) return false;
        }
        // Price range filter
        if (filters.priceRange) {
            const [minPrice, maxPrice] = filters.priceRange;
            const hostelPrices = [];
            if (filters.seaterType && filters.seaterType !== 'all') {
                const seaterPricing = hostel.pricing[filters.seaterType];
                if (filters.acType === 'ac') {
                    hostelPrices.push(seaterPricing.ac);
                } else if (filters.acType === 'non-ac') {
                    hostelPrices.push(seaterPricing.nonAc);
                } else {
                    hostelPrices.push(seaterPricing.ac, seaterPricing.nonAc);
                }
            } else {
                Object.values(hostel.pricing).forEach((seater)=>{
                    if (filters.acType === 'ac') {
                        hostelPrices.push(seater.ac);
                    } else if (filters.acType === 'non-ac') {
                        hostelPrices.push(seater.nonAc);
                    } else {
                        hostelPrices.push(seater.ac, seater.nonAc);
                    }
                });
            }
            const hostelMinPrice = Math.min(...hostelPrices);
            const hostelMaxPrice = Math.max(...hostelPrices);
            if (hostelMaxPrice < minPrice || hostelMinPrice > maxPrice) {
                return false;
            }
        }
        // Amenities filter
        if (filters.amenities && filters.amenities.length > 0) {
            const hasAllAmenities = filters.amenities.every((amenity)=>{
                return hostel.amenities[amenity] === true;
            });
            if (!hasAllAmenities) return false;
        }
        // Distance filter
        if (filters.maxDistance && filters.userLocation) {
            const distance = calculateDistance(filters.userLocation.lat, filters.userLocation.lng, hostel.address.coordinates.lat, hostel.address.coordinates.lng);
            if (distance > filters.maxDistance) return false;
        }
        return true;
    });
}
}}),
"[project]/src/app/api/hostels/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Hostel.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
;
;
;
;
async function GET(request) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { searchParams } = new URL(request.url);
        // Get query parameters
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '12');
        const search = searchParams.get('search') || '';
        const seaterType = searchParams.get('seaterType') || '';
        const acType = searchParams.get('acType') || '';
        const minPrice = searchParams.get('minPrice');
        const maxPrice = searchParams.get('maxPrice');
        const amenities = searchParams.get('amenities')?.split(',').filter(Boolean) || [];
        const sortBy = searchParams.get('sortBy') || 'name';
        const sortOrder = searchParams.get('sortOrder') || 'asc';
        const userLat = searchParams.get('userLat');
        const userLng = searchParams.get('userLng');
        const maxDistance = searchParams.get('maxDistance');
        // Build query
        let query = {
            isActive: true
        };
        // Search functionality
        if (search) {
            query.$or = [
                {
                    name: {
                        $regex: search,
                        $options: 'i'
                    }
                },
                {
                    headline: {
                        $regex: search,
                        $options: 'i'
                    }
                },
                {
                    description: {
                        $regex: search,
                        $options: 'i'
                    }
                },
                {
                    'address.city': {
                        $regex: search,
                        $options: 'i'
                    }
                },
                {
                    'address.state': {
                        $regex: search,
                        $options: 'i'
                    }
                }
            ];
        }
        // Get all hostels first
        let hostels = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find(query).lean();
        // Apply filters
        const filters = {};
        if (seaterType && seaterType !== 'all') {
            filters.seaterType = seaterType;
        }
        if (acType && acType !== 'all') {
            filters.acType = acType;
        }
        if (minPrice && maxPrice) {
            filters.priceRange = [
                parseInt(minPrice),
                parseInt(maxPrice)
            ];
        }
        if (amenities.length > 0) {
            filters.amenities = amenities;
        }
        if (maxDistance && userLat && userLng) {
            filters.maxDistance = parseFloat(maxDistance);
            filters.userLocation = {
                lat: parseFloat(userLat),
                lng: parseFloat(userLng)
            };
        }
        // Apply filters
        hostels = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["filterHostels"])(hostels, filters);
        // Sort hostels
        if (sortBy === 'distance' && userLat && userLng) {
            hostels = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sortHostelsByDistance"])(hostels, parseFloat(userLat), parseFloat(userLng));
        } else {
            hostels.sort((a, b)=>{
                let aValue, bValue;
                switch(sortBy){
                    case 'price':
                        // Get minimum price for sorting
                        const aPrices = Object.values(a.pricing).flatMap((p)=>[
                                p.ac,
                                p.nonAc
                            ]);
                        const bPrices = Object.values(b.pricing).flatMap((p)=>[
                                p.ac,
                                p.nonAc
                            ]);
                        aValue = Math.min(...aPrices);
                        bValue = Math.min(...bPrices);
                        break;
                    case 'name':
                        aValue = a.name.toLowerCase();
                        bValue = b.name.toLowerCase();
                        break;
                    default:
                        aValue = a[sortBy];
                        bValue = b[sortBy];
                }
                if (sortOrder === 'desc') {
                    return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
                } else {
                    return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                }
            });
        }
        // Pagination
        const skip = (page - 1) * limit;
        const paginatedHostels = hostels.slice(skip, skip + limit);
        const totalHostels = hostels.length;
        const totalPages = Math.ceil(totalHostels / limit);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            hostels: paginatedHostels,
            pagination: {
                currentPage: page,
                totalPages,
                totalHostels,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            }
        });
    } catch (error) {
        console.error('Get hostels error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        // Check authentication
        const token = request.cookies.get('auth-token')?.value;
        if (!token) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Authentication required'
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const hostelData = await request.json();
        // Create new hostel
        const hostel = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Hostel$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"](hostelData);
        await hostel.save();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'Hostel created successfully',
            hostel
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Create hostel error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e2485318._.js.map