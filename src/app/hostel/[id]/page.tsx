'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Header from '@/components/Header';
import {
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  StarIcon,
  WifiIcon,
  BuildingOfficeIcon,
  ShieldCheckIcon,
  ClockIcon,
  UserGroupIcon,
  CurrencyRupeeIcon,
  PhotoIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { getCurrentLocation, calculateDistance, formatDistance } from '@/lib/utils';
import toast from 'react-hot-toast';

interface Hostel {
  _id: string;
  name: string;
  headline: string;
  description: string;
  address: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  photos: Array<{
    url: string;
    alt: string;
    category: string;
  }>;
  pricing: {
    '1-seater': { ac: number; nonAc: number };
    '2-seater': { ac: number; nonAc: number };
    '3-seater': { ac: number; nonAc: number };
    '4-seater': { ac: number; nonAc: number };
  };
  roomSizes: {
    '1-seater': string;
    '2-seater': string;
    '3-seater': string;
    '4-seater': string;
  };
  amenities: any;
  nearbyLandmarks: string[];
  nearestColleges: Array<{
    name: string;
    distance: string;
  }>;
  foodMenu: Array<{
    category: string;
    items: Array<{
      name: string;
      price: number;
      description?: string;
    }>;
  }>;
  collegeLunchProvision: boolean;
  securityFeatures: {
    cctv: boolean;
    securityGuard: boolean;
    biometricAccess: boolean;
    timeRestrictions: {
      enabled: boolean;
      inTime: string;
      outTime: string;
    };
  };
  caretaker: {
    name: string;
    phone: string;
    availability: string;
  };
  gymFacilities: string[];
  indoorSportsFacilities: string[];
  eventsAndFests: Array<{
    name: string;
    description: string;
    frequency: string;
  }>;
  travelFacilities: string[];
  owner: {
    name: string;
    phone: string;
    email?: string;
  };
}

export default function HostelDetailPage() {
  const params = useParams();
  const [hostel, setHostel] = useState<Hostel | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userDistance, setUserDistance] = useState<number | null>(null);
  const [selectedPhotoCategory, setSelectedPhotoCategory] = useState<string>('all');
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [showPhotoModal, setShowPhotoModal] = useState(false);

  useEffect(() => {
    if (params.id) {
      fetchHostel();
      getUserLocation();
    }
  }, [params.id]);

  const fetchHostel = async () => {
    try {
      const response = await fetch(`/api/hostels/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        setHostel(data.hostel);
      } else {
        toast.error('Hostel not found');
      }
    } catch (error) {
      toast.error('Failed to fetch hostel details');
    } finally {
      setIsLoading(false);
    }
  };

  const getUserLocation = async () => {
    try {
      const position = await getCurrentLocation();
      if (hostel) {
        const distance = calculateDistance(
          position.coords.latitude,
          position.coords.longitude,
          hostel.address.coordinates.lat,
          hostel.address.coordinates.lng
        );
        setUserDistance(distance);
      }
    } catch (error) {
      console.log('Location access denied');
    }
  };

  useEffect(() => {
    if (hostel) {
      getUserLocation();
    }
  }, [hostel]);

  const getFilteredPhotos = () => {
    if (!hostel) return [];
    if (selectedPhotoCategory === 'all') return hostel.photos;
    return hostel.photos.filter(photo => photo.category === selectedPhotoCategory);
  };

  const photoCategories = [
    { value: 'all', label: 'All Photos' },
    { value: 'main', label: 'Main' },
    { value: '1-seater-ac', label: '1 Seater AC' },
    { value: '1-seater-non-ac', label: '1 Seater Non-AC' },
    { value: '2-seater-ac', label: '2 Seater AC' },
    { value: '2-seater-non-ac', label: '2 Seater Non-AC' },
    { value: '3-seater-ac', label: '3 Seater AC' },
    { value: '3-seater-non-ac', label: '3 Seater Non-AC' },
    { value: '4-seater-ac', label: '4 Seater AC' },
    { value: '4-seater-non-ac', label: '4 Seater Non-AC' },
    { value: 'common-area', label: 'Common Area' },
    { value: 'kitchen', label: 'Kitchen' },
    { value: 'bathroom', label: 'Bathroom' },
    { value: 'exterior', label: 'Exterior' }
  ];

  const amenityIcons: { [key: string]: any } = {
    wifi: WifiIcon,
    parking: BuildingOfficeIcon,
    security: ShieldCheckIcon,
    gym: UserGroupIcon,
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="h-96 bg-gray-300 rounded-lg mb-6"></div>
                <div className="space-y-4">
                  <div className="h-4 bg-gray-300 rounded"></div>
                  <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
              <div>
                <div className="h-64 bg-gray-300 rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!hostel) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <BuildingOfficeIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Hostel Not Found</h1>
            <p className="text-gray-600">The hostel you're looking for doesn't exist.</p>
          </div>
        </div>
      </div>
    );
  }

  const filteredPhotos = getFilteredPhotos();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{hostel.name}</h1>
          <p className="text-lg text-gray-600 mb-4">{hostel.headline}</p>

          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center">
              <MapPinIcon className="h-4 w-4 mr-1" />
              <span>{hostel.address.street}, {hostel.address.city}, {hostel.address.state} - {hostel.address.pincode}</span>
            </div>
            {userDistance && (
              <div className="flex items-center">
                <span>• {formatDistance(userDistance)} from your location</span>
              </div>
            )}
            <div className="flex items-center">
              <StarIcon className="h-4 w-4 text-yellow-400 fill-current mr-1" />
              <span>4.5 (120 reviews)</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Photo Gallery */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Photos</h2>
                <div className="flex items-center space-x-2">
                  <PhotoIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-600">{filteredPhotos.length} photos</span>
                </div>
              </div>

              {/* Photo Category Filter */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {photoCategories.map(category => {
                    const categoryPhotos = category.value === 'all'
                      ? hostel.photos
                      : hostel.photos.filter(p => p.category === category.value);

                    if (categoryPhotos.length === 0 && category.value !== 'all') return null;

                    return (
                      <button
                        key={category.value}
                        onClick={() => {
                          setSelectedPhotoCategory(category.value);
                          setCurrentPhotoIndex(0);
                        }}
                        className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${selectedPhotoCategory === category.value
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                      >
                        {category.label} ({categoryPhotos.length})
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Photo Grid */}
              {filteredPhotos.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {filteredPhotos.slice(0, 6).map((photo, index) => (
                    <div
                      key={index}
                      className="relative aspect-video bg-gray-200 rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => {
                        setCurrentPhotoIndex(index);
                        setShowPhotoModal(true);
                      }}
                    >
                      <img
                        src={photo.url}
                        alt={photo.alt}
                        className="w-full h-full object-cover"
                      />
                      {index === 5 && filteredPhotos.length > 6 && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                          <span className="text-white font-semibold">
                            +{filteredPhotos.length - 6} more
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <PhotoIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">No photos available for this category</p>
                </div>
              )}
            </div>

            {/* Description */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">About This Hostel</h2>
              <p className="text-gray-700 leading-relaxed">{hostel.description}</p>
            </div>

            {/* Pricing */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Pricing & Room Details</h2>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4">Room Type</th>
                      <th className="text-left py-3 px-4">Size</th>
                      <th className="text-left py-3 px-4">AC</th>
                      <th className="text-left py-3 px-4">Non-AC</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(hostel.pricing).map(([type, prices]: [string, any]) => (
                      <tr key={type} className="border-b">
                        <td className="py-3 px-4 font-medium">{type.replace('-', ' ').toUpperCase()}</td>
                        <td className="py-3 px-4 text-gray-600">{hostel.roomSizes[type as keyof typeof hostel.roomSizes]}</td>
                        <td className="py-3 px-4">
                          <span className="text-green-600 font-semibold">₹{prices.ac.toLocaleString()}</span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-blue-600 font-semibold">₹{prices.nonAc.toLocaleString()}</span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Amenities */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Amenities</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {Object.entries(hostel.amenities).map(([key, value]) => {
                  if (typeof value === 'boolean' && value) {
                    const IconComponent = amenityIcons[key] || BuildingOfficeIcon;
                    return (
                      <div key={key} className="flex items-center space-x-2">
                        <IconComponent className="h-5 w-5 text-green-600" />
                        <span className="text-sm capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                      </div>
                    );
                  }
                  return null;
                })}
                {hostel.amenities.other?.map((amenity: string, index: number) => (
                  <div key={index} className="flex items-center space-x-2">
                    <BuildingOfficeIcon className="h-5 w-5 text-green-600" />
                    <span className="text-sm">{amenity}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Food Menu */}
            {hostel.foodMenu.length > 0 && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Food Menu</h2>
                <div className="space-y-6">
                  {hostel.foodMenu.map((category, index) => (
                    <div key={index}>
                      <h3 className="text-lg font-medium text-gray-900 mb-3">{category.category}</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left py-2">Item</th>
                              <th className="text-left py-2">Price</th>
                              <th className="text-left py-2">Description</th>
                            </tr>
                          </thead>
                          <tbody>
                            {category.items.map((item, itemIndex) => (
                              <tr key={itemIndex} className="border-b">
                                <td className="py-2 font-medium">{item.name}</td>
                                <td className="py-2 text-green-600 font-semibold">₹{item.price}</td>
                                <td className="py-2 text-gray-600 text-sm">{item.description || '-'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  ))}
                  {hostel.collegeLunchProvision && (
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-blue-800 font-medium">✓ College lunch provision available</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Facilities */}
            {(hostel.gymFacilities.length > 0 || hostel.indoorSportsFacilities.length > 0) && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Facilities</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {hostel.gymFacilities.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-3">Gym Facilities</h3>
                      <ul className="space-y-2">
                        {hostel.gymFacilities.map((facility, index) => (
                          <li key={index} className="flex items-center space-x-2">
                            <UserGroupIcon className="h-4 w-4 text-green-600" />
                            <span className="text-sm">{facility}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {hostel.indoorSportsFacilities.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-3">Indoor Sports</h3>
                      <ul className="space-y-2">
                        {hostel.indoorSportsFacilities.map((facility, index) => (
                          <li key={index} className="flex items-center space-x-2">
                            <UserGroupIcon className="h-4 w-4 text-blue-600" />
                            <span className="text-sm">{facility}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Events and Travel */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {hostel.eventsAndFests.length > 0 && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold mb-4">Events & Festivals</h3>
                  <div className="space-y-3">
                    {hostel.eventsAndFests.map((event, index) => (
                      <div key={index} className="border-l-4 border-purple-500 pl-4">
                        <h4 className="font-medium text-gray-900">{event.name}</h4>
                        <p className="text-sm text-gray-600">{event.description}</p>
                        <p className="text-xs text-purple-600 font-medium">{event.frequency}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {hostel.travelFacilities.length > 0 && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold mb-4">Travel Facilities</h3>
                  <ul className="space-y-2">
                    {hostel.travelFacilities.map((facility, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <MapPinIcon className="h-4 w-4 text-green-600" />
                        <span className="text-sm">{facility}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Nearby Landmarks */}
            {hostel.nearbyLandmarks.length > 0 && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Nearby Landmarks</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {hostel.nearbyLandmarks.map((landmark, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <MapPinIcon className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-700">{landmark}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Information */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Contact Information</h3>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">Owner</h4>
                  <p className="text-gray-600">{hostel.owner.name}</p>
                  <div className="flex items-center mt-1">
                    <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <a href={`tel:${hostel.owner.phone}`} className="text-blue-600 hover:underline">
                      {hostel.owner.phone}
                    </a>
                  </div>
                  {hostel.owner.email && (
                    <div className="flex items-center mt-1">
                      <EnvelopeIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`mailto:${hostel.owner.email}`} className="text-blue-600 hover:underline">
                        {hostel.owner.email}
                      </a>
                    </div>
                  )}
                </div>

                <div>
                  <h4 className="font-medium text-gray-900">Caretaker</h4>
                  <p className="text-gray-600">{hostel.caretaker.name}</p>
                  <div className="flex items-center mt-1">
                    <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <a href={`tel:${hostel.caretaker.phone}`} className="text-blue-600 hover:underline">
                      {hostel.caretaker.phone}
                    </a>
                  </div>
                  <div className="flex items-center mt-1">
                    <ClockIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-sm text-gray-600">{hostel.caretaker.availability}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Map */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Location</h3>
              <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <MapPinIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Interactive map would be here</p>
                  <p className="text-xs text-gray-500 mt-1">
                    Lat: {hostel.address.coordinates.lat}, Lng: {hostel.address.coordinates.lng}
                  </p>
                </div>
              </div>
            </div>

            {/* Nearby Colleges */}
            {hostel.nearestColleges.length > 0 && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Nearby Colleges</h3>
                <div className="space-y-2">
                  {hostel.nearestColleges.map((college, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm text-gray-700">{college.name}</span>
                      <span className="text-sm text-gray-500">{college.distance}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Security Features */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Security Features</h3>
              <div className="space-y-2">
                {hostel.securityFeatures.cctv && (
                  <div className="flex items-center space-x-2">
                    <ShieldCheckIcon className="h-4 w-4 text-green-600" />
                    <span className="text-sm">CCTV Surveillance</span>
                  </div>
                )}
                {hostel.securityFeatures.securityGuard && (
                  <div className="flex items-center space-x-2">
                    <ShieldCheckIcon className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Security Guard</span>
                  </div>
                )}
                {hostel.securityFeatures.biometricAccess && (
                  <div className="flex items-center space-x-2">
                    <ShieldCheckIcon className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Biometric Access</span>
                  </div>
                )}
                {hostel.securityFeatures.timeRestrictions.enabled && (
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="h-4 w-4 text-orange-600" />
                    <span className="text-sm">
                      Time Restrictions: {hostel.securityFeatures.timeRestrictions.inTime} - {hostel.securityFeatures.timeRestrictions.outTime}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Photo Modal */}
      {showPhotoModal && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
          <div className="relative max-w-4xl max-h-full p-4">
            <button
              onClick={() => setShowPhotoModal(false)}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            >
              <XMarkIcon className="h-8 w-8" />
            </button>

            {filteredPhotos.length > 0 && (
              <>
                <img
                  src={filteredPhotos[currentPhotoIndex]?.url}
                  alt={filteredPhotos[currentPhotoIndex]?.alt}
                  className="max-w-full max-h-full object-contain"
                />

                {filteredPhotos.length > 1 && (
                  <>
                    <button
                      onClick={() => setCurrentPhotoIndex(
                        currentPhotoIndex === 0 ? filteredPhotos.length - 1 : currentPhotoIndex - 1
                      )}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300"
                    >
                      <ChevronLeftIcon className="h-8 w-8" />
                    </button>

                    <button
                      onClick={() => setCurrentPhotoIndex(
                        currentPhotoIndex === filteredPhotos.length - 1 ? 0 : currentPhotoIndex + 1
                      )}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300"
                    >
                      <ChevronRightIcon className="h-8 w-8" />
                    </button>

                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white">
                      {currentPhotoIndex + 1} / {filteredPhotos.length}
                    </div>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
